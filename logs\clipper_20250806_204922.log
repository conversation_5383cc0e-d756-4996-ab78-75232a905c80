2025-08-06 20:49:22,888 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 20:49:24,942 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:49:24,943 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 20:49:26,980 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:49:26,981 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 20:49:26,981 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:49:26,981 - clipper_main - INFO - Step 1: Extracting transcript...
2025-08-06 20:49:27,347 - transcription_engine - INFO - Accessing YouTube transcript data: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:49:34,696 - transcription_engine - WARNING - No transcript data available for this video
2025-08-06 20:49:34,717 - transcription_engine - INFO - Trying youtube-transcript-api for video: xKa_6kzOl0M
2025-08-06 20:49:34,718 - transcription_engine - WARNING - youtube-transcript-api: No transcript found in any language
2025-08-06 20:49:34,718 - transcription_engine - ERROR - TRANSCRIPT EXTRACTION FAILED: No transcript available from any method
2025-08-06 20:49:34,718 - transcription_engine - ERROR - Pipeline will stop - cannot generate clips without transcript
2025-08-06 20:49:34,718 - clipper_main - ERROR - PIPELINE STOPPED: No transcript available for this video
2025-08-06 20:49:34,718 - clipper_main - ERROR - Error processing video https://www.youtube.com/watch?v=xKa_6kzOl0M: No transcript available - cannot proceed with clip generation
