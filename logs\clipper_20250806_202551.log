2025-08-06 20:25:51,468 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 20:25:53,499 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:25:53,500 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 20:25:55,534 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:25:55,535 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 20:25:55,535 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:25:55,535 - clipper_main - INFO - Step 1: Downloading video...
2025-08-06 20:25:55,535 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:25:55,535 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:25:59,627 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 20:25:59,627 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 20:26:03,954 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 20:26:03,954 - clipper_main - INFO - Step 2: Generating transcript...
2025-08-06 20:26:04,328 - transcription_engine - INFO - Accessing YouTube transcript data: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:26:05,358 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=TargetClosedError('Target page, context or browser has been closed')>
playwright._impl._errors.TargetClosedError: Target page, context or browser has been closed
