2025-08-06 19:36:11,950 - transcription_engine - INFO - YouTube Transcript API available for perfect timestamps
2025-08-06 19:36:13,437 - transcription_engine - INFO - Enhanced whisper-timestamped available for accurate timestamps
2025-08-06 19:36:15,468 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 19:36:15,469 - transcription_engine - INFO - YouTube Transcript API available for perfect timestamps
2025-08-06 19:36:15,469 - transcription_engine - INFO - Enhanced whisper-timestamped available for accurate timestamps
2025-08-06 19:36:17,500 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 19:36:17,501 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 19:36:17,502 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 19:36:17,502 - clipper_main - INFO - Step 1: Downloading video...
2025-08-06 19:36:17,502 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 19:36:17,502 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 19:36:21,733 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 19:36:21,733 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 19:36:26,377 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 19:36:26,377 - clipper_main - INFO - Step 2: Generating transcript...
2025-08-06 19:36:26,377 - transcription_engine - INFO - Starting transcription for: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 19:36:26,382 - transcription_engine - INFO - Fetching YouTube transcript for video ID: xKa_6kzOl0M
2025-08-06 19:36:26,382 - transcription_engine - WARNING - YouTube transcript extraction failed: type object 'YouTubeTranscriptApi' has no attribute 'get_transcript'
2025-08-06 19:36:26,382 - transcription_engine - INFO - Extracting audio from video...
2025-08-06 19:36:26,767 - transcription_engine - INFO - Audio extracted: C:\Users\<USER>\AppData\Local\Temp\tmpni7se5ps.wav (21.7 MB)
2025-08-06 19:36:26,767 - transcription_engine - INFO - Loading enhanced Whisper model...
2025-08-06 19:36:27,207 - transcription_engine - INFO - Transcribing with enhanced Whisper (accurate timestamps)...
2025-08-06 19:38:48,557 - transcription_engine - INFO - Enhanced Whisper transcription completed. Language: en
2025-08-06 19:38:48,558 - transcription_engine - INFO - Transcription completed. Length: 13122 characters
2025-08-06 19:38:48,561 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 19:38:48,561 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 19:39:25,494 - highlight_detector - INFO - Parsed 2 highlights from AI response
2025-08-06 19:39:25,494 - highlight_detector - INFO - Raw highlights before filtering: 2
2025-08-06 19:39:25,495 - highlight_detector - INFO - Skipping long highlight: 'The Soul Check' duration=73.7s (max=60.0s)
2025-08-06 19:39:25,497 - highlight_detector - INFO - Detected 1 valid highlights
2025-08-06 19:39:25,497 - clipper_main - INFO - Step 4: Extracting video clips...
2025-08-06 19:39:25,497 - video_clipper - INFO - Extracting clip 1: The Breakup Announcement (120.5s-148.3s)
2025-08-06 19:39:35,410 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_01_The_Breakup_Announcement.mp4 (8.4 MB)
2025-08-06 19:39:35,410 - clipper_main - INFO - Step 5: Post-processing clips...
2025-08-06 19:39:35,410 - post_processor - INFO - Post-processing 1 clips
2025-08-06 19:39:35,410 - clipper_main - ERROR - Error processing video https://www.youtube.com/watch?v=xKa_6kzOl0M: [WinError 3] The system cannot find the path specified: 'C:\\AI-Hub\\Applications\\Clipper_Neon\\data_out\\20250806_193935_we_broke_up..\\clips'
