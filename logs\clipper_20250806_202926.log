2025-08-06 20:29:26,970 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 20:29:29,015 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:29:29,016 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 20:29:31,065 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:29:31,066 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 20:29:31,066 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:29:31,066 - clipper_main - INFO - Step 1: Downloading video...
2025-08-06 20:29:31,066 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:29:31,066 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:29:35,031 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 20:29:35,031 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 20:29:39,710 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 20:29:39,710 - clipper_main - INFO - Step 2: Generating transcript...
2025-08-06 20:29:40,084 - transcription_engine - INFO - Accessing YouTube transcript data: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:29:46,223 - transcription_engine - WARNING - No transcript data available for this video
2025-08-06 20:29:46,230 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 20:29:46,232 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 20:30:03,519 - highlight_detector - ERROR - Failed to parse JSON response: Extra data: line 16 column 1 (char 271)
2025-08-06 20:30:03,519 - highlight_detector - WARNING - Using fallback highlight detection
2025-08-06 20:30:03,519 - highlight_detector - INFO - Raw highlights before filtering: 3
2025-08-06 20:30:03,523 - highlight_detector - INFO - Detected 3 valid highlights
2025-08-06 20:30:03,523 - clipper_main - INFO - Step 4: Extracting video clips...
2025-08-06 20:30:03,523 - video_clipper - INFO - Extracting clip 1: Highlight 1 (10.0s-55.0s)
2025-08-06 20:30:10,795 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_01_Highlight_1.mp4 (9.9 MB)
2025-08-06 20:30:10,795 - video_clipper - INFO - Extracting clip 2: Highlight 2 (188.2s-233.2s)
2025-08-06 20:30:25,083 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_02_Highlight_2.mp4 (12.6 MB)
2025-08-06 20:30:25,084 - video_clipper - INFO - Extracting clip 3: Highlight 3 (366.5s-411.5s)
2025-08-06 20:30:44,751 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_03_Highlight_3.mp4 (12.7 MB)
2025-08-06 20:30:44,751 - clipper_main - INFO - Step 5: Organizing clips...
2025-08-06 20:30:44,753 - clipper_main - INFO - Successfully generated 3 clips
