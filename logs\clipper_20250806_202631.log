2025-08-06 20:26:31,257 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 20:26:33,273 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:26:33,273 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 20:26:35,289 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:26:35,289 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 20:26:35,290 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:26:35,290 - clipper_main - INFO - Step 1: Downloading video...
2025-08-06 20:26:35,290 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:26:35,290 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:26:39,286 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 20:26:39,287 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 20:26:43,946 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 20:26:43,946 - clipper_main - INFO - Step 2: Generating transcript...
2025-08-06 20:26:44,338 - transcription_engine - INFO - Accessing YouTube transcript data: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:26:50,610 - transcription_engine - WARNING - No transcript data available for this video
2025-08-06 20:26:50,620 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 20:26:50,620 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 20:27:28,441 - highlight_detector - INFO - Parsed 5 highlights from AI response
2025-08-06 20:27:28,441 - highlight_detector - INFO - Raw highlights before filtering: 5
2025-08-06 20:27:28,443 - highlight_detector - INFO - Skipping long highlight: 'SHOCKING_Breakup_He_WANTED_Me_Gone_So' duration=63.8s (max=60.0s)
2025-08-06 20:27:28,443 - highlight_detector - INFO - Skipping long highlight: 'CELEBRITY_Breakup_They_SPOKE_ABOUT_My' duration=62.6s (max=60.0s)
2025-08-06 20:27:28,443 - highlight_detector - INFO - Skipping long highlight: 'PSYCHO_Ex_Threatened_Me_LIVE_on' duration=76.7s (max=60.0s)
2025-08-06 20:27:28,443 - highlight_detector - INFO - Skipping long highlight: 'INSANE_Breakup_We_WERE_Setting_Up_A' duration=92.5s (max=60.0s)
2025-08-06 20:27:28,443 - highlight_detector - INFO - Detected 1 valid highlights
2025-08-06 20:27:28,443 - clipper_main - INFO - Step 4: Extracting video clips...
2025-08-06 20:27:28,443 - video_clipper - INFO - Extracting clip 1: INSANE_Breakup_She_TOLD_Me_ABOUT_This_Liar (120.4s-158.7s)
2025-08-06 20:27:38,134 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_01_INSANE_Breakup_She_TOLD_Me_ABOUT_This_Liar.mp4 (11.4 MB)
2025-08-06 20:27:38,134 - clipper_main - INFO - Step 5: Organizing clips...
2025-08-06 20:27:38,134 - clipper_main - ERROR - Error processing video https://www.youtube.com/watch?v=xKa_6kzOl0M: [WinError 3] The system cannot find the path specified
