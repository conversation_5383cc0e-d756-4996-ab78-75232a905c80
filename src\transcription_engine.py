"""
Direct YouTube Transcript Engine for Clipper_Neon
=================================================

Tesla Assembly Line Approach: Automate the exact manual process
- Go to YouTube video
- Access transcript data directly from DOM  
- Get perfect timestamps from YouTube itself
- Zero APIs, Zero costs, Perfect accuracy
"""

import os
import time
import logging
from typing import Dict, List, Optional
from urllib.parse import urlparse, parse_qs


class TranscriptionEngine:
    """
    Direct YouTube Transcript Engine
    
    Mimics exactly what you do manually:
    1. Go to YouTube video
    2. Access transcript data directly from DOM
    3. Get perfect timestamps from YouTube itself
    """
    
    def __init__(self, config):
        """Initialize the direct transcript engine."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Check for browser automation
        self.playwright_available = self._check_playwright()
        if self.playwright_available:
            self.logger.info("Direct YouTube transcript extraction ready")
        else:
            self.logger.warning("Install playwright for direct transcript access: pip install playwright && playwright install chromium")
    
    def _check_playwright(self) -> bool:
        """Check if <PERSON><PERSON> is available for direct YouTube access."""
        try:
            from playwright.sync_api import sync_playwright
            return True
        except ImportError:
            return False
    
    def transcribe(self, video_path: str, youtube_url: str = None) -> List[Dict]:
        """Get transcript directly from YouTube - the manual way, automated"""

        if not youtube_url:
            self.logger.error("YouTube URL required for direct transcript extraction")
            return self._fallback()

        # Extract video ID from URL
        video_id = self._extract_video_id(youtube_url)
        if not video_id:
            self.logger.error(f"Could not extract video ID from URL: {youtube_url}")
            return self._fallback()

        # Use clean YouTube URL format
        video_url = f"https://www.youtube.com/watch?v={video_id}"
        
        # Try 1: Direct Playwright extraction (best quality)
        if self.playwright_available:
            transcript = self._extract_direct_from_youtube(video_url)
            if transcript:
                return transcript

        # Try 2: youtube-transcript-api fallback (reliable)
        transcript = self._extract_with_youtube_transcript_api(video_id)
        if transcript:
            return transcript

        # Try 3: Final fallback
        return self._fallback()
    
    def _extract_direct_from_youtube(self, video_url: str) -> Optional[List[Dict]]:
        """Bulletproof transcript extraction via UI interaction"""
        try:
            from playwright.sync_api import sync_playwright

            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                page = browser.new_page()

                self.logger.info(f"Opening YouTube video: {video_url}")
                page.goto(video_url, wait_until="networkidle")
                time.sleep(3)

                # Step 1: Try to open transcript panel (multiple methods)
                transcript_opened = False

                # Method 1: Direct transcript button
                transcript_selectors = [
                    'text=Show transcript',
                    'button[aria-label*="Show transcript"]',
                    'yt-button-shape:has-text("Show transcript")',
                    '[aria-label="Show transcript"]',
                    '[aria-label*="Transcript"]',
                ]

                for selector in transcript_selectors:
                    try:
                        btn = page.query_selector(selector)
                        if btn and btn.is_visible():
                            self.logger.info(f"Found transcript button: {selector}")
                            btn.click()
                            transcript_opened = True
                            break
                    except Exception as e:
                        self.logger.debug(f"Selector {selector} failed: {e}")
                        continue

                # Method 2: Try expanding description/more menu first
                if not transcript_opened:
                    try:
                        # Try expanding description
                        more_buttons = [
                            'tp-yt-paper-button#expand',
                            'button[aria-label*="more"]',
                            'button[aria-label*="Show more"]',
                            '#expand'
                        ]

                        for more_selector in more_buttons:
                            try:
                                more_btn = page.query_selector(more_selector)
                                if more_btn and more_btn.is_visible():
                                    self.logger.info(f"Expanding description with: {more_selector}")
                                    more_btn.click()
                                    time.sleep(1)
                                    break
                            except:
                                continue

                        # Now try transcript button again
                        for selector in transcript_selectors:
                            try:
                                btn = page.query_selector(selector)
                                if btn and btn.is_visible():
                                    self.logger.info(f"Found transcript button after expansion: {selector}")
                                    btn.click()
                                    transcript_opened = True
                                    break
                            except:
                                continue

                    except Exception as e:
                        self.logger.debug(f"Description expansion failed: {e}")

                if not transcript_opened:
                    self.logger.error("Could not open transcript panel - button not found")
                    browser.close()
                    return None

                # Step 2: Wait for transcript segments to load
                self.logger.info("Waiting for transcript segments to load...")
                segment_loaded = False
                for attempt in range(20):  # Wait up to 10 seconds
                    if page.query_selector('ytd-transcript-segment-renderer'):
                        segment_loaded = True
                        break
                    time.sleep(0.5)

                if not segment_loaded:
                    self.logger.error("Transcript panel opened but no segments loaded")
                    browser.close()
                    return None

                # Step 3: Extract transcript segments
                self.logger.info("Extracting transcript segments...")
                segments = page.query_selector_all('ytd-transcript-segment-renderer')
                transcript = []

                for segment in segments:
                    try:
                        # Extract timestamp and text with multiple fallback selectors
                        timestamp_selectors = [
                            '.segment-timestamp',
                            'tp-yt-paper-button span',
                            '.ytd-transcript-segment-renderer button span',
                            'button[role="button"] span'
                        ]

                        text_selectors = [
                            '.segment-text',
                            'span[aria-label]',
                            '.ytd-transcript-segment-renderer span:not(button span)',
                            'yt-formatted-string'
                        ]

                        timestamp = ""
                        text = ""

                        # Get timestamp
                        for ts_selector in timestamp_selectors:
                            try:
                                ts_el = segment.query_selector(ts_selector)
                                if ts_el:
                                    timestamp = ts_el.inner_text().strip()
                                    if timestamp:
                                        break
                            except:
                                continue

                        # Get text
                        for txt_selector in text_selectors:
                            try:
                                txt_el = segment.query_selector(txt_selector)
                                if txt_el:
                                    text = txt_el.inner_text().strip()
                                    if text and text != timestamp:  # Make sure it's not the timestamp
                                        break
                            except:
                                continue

                        if timestamp and text:
                            # Convert timestamp to seconds
                            start_seconds = self._timestamp_to_seconds(timestamp)
                            if start_seconds is not None:
                                transcript.append({
                                    'text': text,
                                    'start': start_seconds,
                                    'end': start_seconds + 3.0,  # Default 3 second duration
                                    'duration': 3.0,
                                    'confidence': 1.0
                                })

                    except Exception as e:
                        self.logger.debug(f"Failed to extract segment: {e}")
                        continue

                browser.close()

                if transcript and len(transcript) > 0:
                    self.logger.info(f"[SUCCESS] Extracted {len(transcript)} transcript segments via UI!")
                    return transcript
                else:
                    self.logger.warning("No transcript segments extracted")
                    return None

        except Exception as e:
            self.logger.error(f"UI transcript extraction failed: {e}")
            return None

    def _timestamp_to_seconds(self, timestamp: str) -> Optional[float]:
        """Convert timestamp string (e.g., '1:23' or '0:45') to seconds."""
        try:
            import re
            # Handle formats like "1:23", "0:45", "12:34"
            match = re.match(r'(\d+):(\d+)', timestamp)
            if match:
                minutes = int(match.group(1))
                seconds = int(match.group(2))
                return float(minutes * 60 + seconds)
            return None
        except:
            return None

    def _extract_with_youtube_transcript_api(self, video_id: str) -> Optional[List[Dict]]:
        """Fallback using youtube-transcript-api library."""
        try:
            from youtube_transcript_api import YouTubeTranscriptApi

            self.logger.info(f"Trying youtube-transcript-api for video: {video_id}")

            # Try multiple language options
            languages = ['en', 'en-US', 'en-GB', 'auto']

            for lang in languages:
                try:
                    transcript_list = YouTubeTranscriptApi.get_transcript(video_id, languages=[lang])

                    if transcript_list:
                        # Convert to our format
                        formatted_transcript = []
                        for entry in transcript_list:
                            formatted_transcript.append({
                                'text': entry['text'],
                                'start': entry['start'],
                                'end': entry['start'] + entry['duration'],
                                'duration': entry['duration'],
                                'confidence': 0.9  # youtube-transcript-api is reliable
                            })

                        self.logger.info(f"[SUCCESS] Got {len(formatted_transcript)} transcript segments via youtube-transcript-api!")
                        return formatted_transcript

                except Exception as e:
                    self.logger.debug(f"Language {lang} failed: {e}")
                    continue

            self.logger.warning("youtube-transcript-api: No transcript found in any language")
            return None

        except ImportError:
            self.logger.warning("youtube-transcript-api not installed. Install with: pip install youtube-transcript-api")
            return None
        except Exception as e:
            self.logger.error(f"youtube-transcript-api extraction failed: {e}")
            return None

    def _fallback(self) -> List[Dict]:
        """Fallback when transcript not available - return empty to stop pipeline"""
        self.logger.error("TRANSCRIPT EXTRACTION FAILED: No transcript available from any method")
        self.logger.error("Pipeline will stop - cannot generate clips without transcript")
        return []  # Return empty list to stop pipeline

    def _extract_video_id(self, youtube_url: str) -> str:
        """Extract video ID from YouTube URL."""
        parsed_url = urlparse(youtube_url)
        if parsed_url.hostname == 'youtu.be':
            return parsed_url.path[1:]
        elif parsed_url.hostname in ('www.youtube.com', 'youtube.com'):
            if parsed_url.path == '/watch':
                return parse_qs(parsed_url.query)['v'][0]
            elif parsed_url.path[:7] == '/embed/':
                return parsed_url.path.split('/')[2]
            elif parsed_url.path[:3] == '/v/':
                return parsed_url.path.split('/')[2]
        return ""


if __name__ == "__main__":
    # Test the direct YouTube transcript engine
    from config import ClipperConfig
    
    config = ClipperConfig()
    transcriber = TranscriptionEngine(config)
    
    print("[ENGINE] Direct YouTube Transcript Engine")
    print(f"[OK] Playwright available: {transcriber.playwright_available}")
    print("[READY] Ready for Tesla assembly line transcript extraction!")
    
    # Test with a video ID
    # transcript = transcriber.transcribe("", video_id="xKa_6kzOl0M")
    # print(f"Transcript segments: {len(transcript)}")
