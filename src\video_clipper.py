"""
Video Clipper Module for Clipper_Neon
=====================================

Handles video clipping using ffmpeg based on detected highlights.
"""

import os
import subprocess
import logging
from pathlib import Path
from typing import Optional
from datetime import timed<PERSON><PERSON>

from highlight_detector import Highlight


class VideoClipper:
    """Handles video clipping using ffmpeg."""
    
    def __init__(self, config):
        """Initialize the video clipper."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Verify ffmpeg is available
        if not Path(self.config.ffmpeg_path).exists():
            raise FileNotFoundError(f"ffmpeg not found at: {self.config.ffmpeg_path}")
    
    def extract_clip(
        self,
        video_path: str,
        highlight: Highlight,
        output_dir: str,
        clip_index: int = 0
    ) -> str:
        """
        Extract a video clip based on highlight timestamps.
        
        Args:
            video_path: Path to source video file
            highlight: Highlight object with timing and metadata
            output_dir: Directory to save the clip
            clip_index: Index for naming the clip file
            
        Returns:
            Path to the extracted clip file
        """
        self.logger.info(
            f"Extracting clip {clip_index + 1}: {highlight.title} "
            f"({highlight.start_time:.1f}s-{highlight.end_time:.1f}s)"
        )
        
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Generate output filename
        safe_title = self._sanitize_filename(highlight.title)
        output_filename = f"clip_{clip_index + 1:02d}_{safe_title}.{self.config.clip_format}"
        output_file = output_path / output_filename
        
        # Build ffmpeg command
        cmd = self._build_ffmpeg_command(
            video_path,
            str(output_file),
            highlight.start_time,
            highlight.end_time
        )
        
        try:
            # Execute ffmpeg
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )
            
            # Verify output file was created
            if not output_file.exists():
                raise FileNotFoundError(f"Clip file was not created: {output_file}")
            
            file_size = output_file.stat().st_size
            self.logger.info(f"Clip extracted successfully: {output_file} ({file_size / 1024 / 1024:.1f} MB)")
            
            return str(output_file)
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"ffmpeg failed: {e.stderr}")
            raise
        except Exception as e:
            self.logger.error(f"Error extracting clip: {e}")
            raise
    
    def _build_ffmpeg_command(
        self,
        input_path: str,
        output_path: str,
        start_time: float,
        end_time: float
    ) -> list:
        """Build ffmpeg command for clip extraction."""
        duration = end_time - start_time
        
        cmd = [
            self.config.ffmpeg_path,
            "-i", input_path,
            "-ss", str(start_time),
            "-t", str(duration),
            "-c:v", "libx264",  # Video codec
            "-c:a", "aac",      # Audio codec
            "-preset", "fast",   # Encoding speed
            "-crf", "23",       # Quality (lower = better quality)
            "-movflags", "+faststart",  # Optimize for streaming
            "-y",               # Overwrite output file
            output_path
        ]
        
        # Add additional options based on config
        if self.config.video_quality == "720p":
            cmd.extend(["-vf", "scale=-2:720"])
        elif self.config.video_quality == "480p":
            cmd.extend(["-vf", "scale=-2:480"])
        elif self.config.video_quality == "360p":
            cmd.extend(["-vf", "scale=-2:360"])
        
        return cmd
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility."""
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Limit length and remove extra spaces
        filename = filename.strip()[:50]
        filename = '_'.join(filename.split())  # Replace spaces with underscores
        
        return filename or "untitled"
    
    def extract_multiple_clips(
        self,
        video_path: str,
        highlights: list,
        output_dir: str
    ) -> list:
        """
        Extract multiple clips from a video.
        
        Args:
            video_path: Path to source video file
            highlights: List of Highlight objects
            output_dir: Directory to save clips
            
        Returns:
            List of paths to extracted clip files
        """
        self.logger.info(f"Extracting {len(highlights)} clips from {video_path}")
        
        clip_paths = []
        
        for i, highlight in enumerate(highlights):
            try:
                clip_path = self.extract_clip(video_path, highlight, output_dir, i)
                clip_paths.append(clip_path)
            except Exception as e:
                self.logger.error(f"Failed to extract clip {i + 1}: {e}")
                continue
        
        self.logger.info(f"Successfully extracted {len(clip_paths)} out of {len(highlights)} clips")
        return clip_paths
    
    def get_video_info(self, video_path: str) -> dict:
        """Get video information using ffprobe."""
        cmd = [
            self.config.ffmpeg_path.replace("ffmpeg.exe", "ffprobe.exe"),
            "-v", "quiet",
            "-print_format", "json",
            "-show_format",
            "-show_streams",
            video_path
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )
            
            import json
            return json.loads(result.stdout)
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"ffprobe failed: {e.stderr}")
            return {}
        except Exception as e:
            self.logger.error(f"Error getting video info: {e}")
            return {}
    
    def create_preview_thumbnails(
        self,
        video_path: str,
        highlights: list,
        output_dir: str
    ) -> list:
        """Create thumbnail images for each highlight."""
        self.logger.info("Creating preview thumbnails...")
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        thumbnail_paths = []
        
        for i, highlight in enumerate(highlights):
            try:
                # Use middle of highlight for thumbnail
                thumbnail_time = (highlight.start_time + highlight.end_time) / 2
                
                safe_title = self._sanitize_filename(highlight.title)
                thumbnail_filename = f"thumb_{i + 1:02d}_{safe_title}.jpg"
                thumbnail_path = output_path / thumbnail_filename
                
                cmd = [
                    self.config.ffmpeg_path,
                    "-i", video_path,
                    "-ss", str(thumbnail_time),
                    "-vframes", "1",
                    "-q:v", "2",  # High quality
                    "-y",
                    str(thumbnail_path)
                ]
                
                subprocess.run(cmd, capture_output=True, check=True)
                
                if thumbnail_path.exists():
                    thumbnail_paths.append(str(thumbnail_path))
                    self.logger.debug(f"Created thumbnail: {thumbnail_path}")
                
            except Exception as e:
                self.logger.warning(f"Failed to create thumbnail for clip {i + 1}: {e}")
                continue
        
        return thumbnail_paths
    
    def format_timestamp(self, seconds: float) -> str:
        """Format seconds as HH:MM:SS timestamp."""
        td = timedelta(seconds=seconds)
        hours, remainder = divmod(td.total_seconds(), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"


if __name__ == "__main__":
    # Test the video clipper
    from config import ClipperConfig
    from highlight_detector import Highlight
    
    config = ClipperConfig()
    clipper = VideoClipper(config)
    
    # Test with sample highlight
    sample_highlight = Highlight(
        start_time=30.0,
        end_time=60.0,
        title="Test Highlight",
        description="A test highlight for demonstration",
        confidence=0.9,
        keywords=["test"]
    )
    
    print(f"Video clipper initialized with ffmpeg at: {config.ffmpeg_path}")
    print(f"Sample highlight: {sample_highlight.title} ({clipper.format_timestamp(sample_highlight.start_time)}-{clipper.format_timestamp(sample_highlight.end_time)})")
