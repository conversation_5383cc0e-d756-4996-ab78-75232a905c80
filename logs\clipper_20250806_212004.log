2025-08-06 21:20:04,875 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 21:20:06,928 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 21:20:06,930 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 21:20:08,975 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 21:20:08,976 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 21:20:08,976 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:20:08,976 - clipper_main - INFO - Step 1: Extracting transcript...
2025-08-06 21:20:09,351 - transcription_engine - INFO - Opening YouTube video: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:20:29,463 - transcription_engine - INFO - Expanding description with: tp-yt-paper-button#expand
2025-08-06 21:20:30,510 - transcription_engine - INFO - Found transcript button after expansion: text=Show transcript
2025-08-06 21:20:30,556 - transcription_engine - INFO - Waiting for transcript segments to load...
2025-08-06 21:20:31,593 - transcription_engine - INFO - Extracting transcript segments...
2025-08-06 21:20:34,477 - transcription_engine - INFO - [SUCCESS] Extracted 340 transcript segments via UI!
2025-08-06 21:20:34,489 - clipper_main - INFO - [SUCCESS] Transcript extracted: 340 segments
2025-08-06 21:20:34,490 - clipper_main - INFO - Step 2: Downloading video...
2025-08-06 21:20:34,490 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:20:34,490 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:20:38,585 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 21:20:38,585 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 21:20:43,497 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 21:20:43,497 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 21:20:43,497 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 21:21:41,637 - highlight_detector - ERROR - Failed to parse JSON response: Expecting property name enclosed in double quotes: line 13 column 7 (char 485)
2025-08-06 21:21:41,637 - highlight_detector - ERROR - Raw response (first 500 chars): <think>
The user provided a JSON object as an example but the description is missing some fields. Let's fix it properly.
</think>
{
  "highlights": [
    {
      "start_time": 0,
      "end_time": 45.3,
      "title": "INSANE_Breakup_Unexpected_Purchase_Turns_Her_Dream_to_Chef",
      "description": "A man in a suit and tie, with messy hair, looks into the camera and says 'I'm going to die' during an emotional moment, creating a jarring contrast between his usual content style.",
      "viral_co
2025-08-06 21:21:41,638 - highlight_detector - ERROR - Cleaned JSON attempt: {
  "highlights": [
    {
      "start_time": 0,
      "end_time": 45.3,
      "title": "INSANE_Breakup_Unexpected_Purchase_Turns_Her_Dream_to_Chef",
      "description": "A man in a suit and tie, wit
2025-08-06 21:21:41,638 - highlight_detector - WARNING - Using fallback highlight detection
2025-08-06 21:21:41,638 - highlight_detector - INFO - Raw highlights before filtering: 3
2025-08-06 21:21:41,638 - highlight_detector - INFO - [OK] Accepted highlight: 'Highlight 1' (10.0s-55.0s) conf=0.60
2025-08-06 21:21:41,638 - highlight_detector - INFO - [OK] Accepted highlight: 'Highlight 2' (188.2s-233.2s) conf=0.60
2025-08-06 21:21:41,639 - highlight_detector - INFO - [OK] Accepted highlight: 'Highlight 3' (366.5s-411.5s) conf=0.60
2025-08-06 21:21:41,639 - highlight_detector - INFO - Detected 3 valid highlights
2025-08-06 21:21:41,639 - clipper_main - INFO - Step 4: Creating target folder and extracting clips...
2025-08-06 21:21:41,640 - clipper_main - INFO - Created target folder: 6. we broke up
2025-08-06 21:21:41,640 - video_clipper - INFO - Extracting clip 1: Highlight 1 (10.0s-55.0s)
2025-08-06 21:21:49,840 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\6. we broke up\clip_01_Highlight_1.mp4 (9.9 MB)
2025-08-06 21:21:49,840 - video_clipper - INFO - Extracting clip 2: Highlight 2 (188.2s-233.2s)
2025-08-06 21:22:04,534 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\6. we broke up\clip_02_Highlight_2.mp4 (12.6 MB)
2025-08-06 21:22:04,534 - video_clipper - INFO - Extracting clip 3: Highlight 3 (366.5s-411.5s)
2025-08-06 21:22:25,121 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\6. we broke up\clip_03_Highlight_3.mp4 (12.7 MB)
2025-08-06 21:22:25,121 - clipper_main - INFO - Step 5: Post-processing clips with viral enhancement...
2025-08-06 21:22:29,984 - clipper_main - INFO - Enhanced title for clip 1: INSANE_AI_Generated_Viral_Content_Ready_For
2025-08-06 21:22:34,684 - clipper_main - INFO - Enhanced title for clip 2: INSANE_AI_Generated_Viral_Content_Ready_For
2025-08-06 21:22:39,387 - clipper_main - INFO - Enhanced title for clip 3: INSANE_AI_Generated_Viral_Content_Ready_For
2025-08-06 21:22:39,387 - clipper_main - INFO - Step 6: Generating clips metadata...
2025-08-06 21:22:39,387 - clipper_main - INFO - Generated clips metadata: C:\AI-Hub\Applications\Clipper_Neon\data_out\6. we broke up\clips_info.json
2025-08-06 21:22:39,388 - clipper_main - INFO - Successfully generated 3 clips
