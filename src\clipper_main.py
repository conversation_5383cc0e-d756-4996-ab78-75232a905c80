#!/usr/bin/env python3
"""
Clipper_Neon - YouTube Video Highlight Extraction Automation
============================================================

Main entry point for the YouTube clipping automation system.
Leverages DeepSeek-R1 for intelligent highlight detection.

Author: AI-Hub Automation System
Version: 1.0.0
"""

import os
import sys
import json
import logging
import argparse
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime

# Add project root to path for imports
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.append(str(PROJECT_ROOT))

# Import our modules
from video_downloader import VideoDownloader
from transcription_engine import TranscriptionEngine
from highlight_detector import HighlightDetector, Highlight
from video_clipper import VideoClipper
from post_processor import PostProcessor
from viral_organizer import ViralOrganizer
from config import ClipperConfig


@dataclass
class HighlightClip:
    """Data class for a detected highlight clip."""
    start_time: float
    end_time: float
    title: str
    description: str
    confidence: float
    keywords: List[str]


class ClipperNeon:
    """Main orchestrator for the YouTube clipping automation."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the Clipper_Neon system."""
        self.config = ClipperConfig(config_path)
        self.setup_logging()
        
        # Initialize components
        self.downloader = VideoDownloader(self.config)
        self.transcriber = TranscriptionEngine(self.config)
        self.highlight_detector = HighlightDetector(self.config)
        self.clipper = VideoClipper(self.config)
        self.post_processor = PostProcessor(self.config)
        self.viral_organizer = ViralOrganizer(self.config)
        
        self.logger = logging.getLogger(__name__)
        
    def setup_logging(self):
        """Configure logging for the application."""
        log_dir = PROJECT_ROOT / "logs"
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / f"clipper_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=getattr(logging, self.config.log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    def process_video(self, youtube_url: str, output_dir: Optional[str] = None) -> List[str]:
        """
        Process a single YouTube video and extract highlights.

        Args:
            youtube_url: YouTube video URL
            output_dir: Optional output directory for clips

        Returns:
            List of paths to generated clip files
        """
        self.logger.info(f"Starting processing for: {youtube_url}")
        
        try:
            # Step 1: Download video
            self.logger.info("Step 1: Downloading video...")
            video_path, metadata = self.downloader.download(youtube_url)
            
            # Step 2: Extract audio and generate transcript
            self.logger.info("Step 2: Generating transcript...")
            transcript = self.transcriber.transcribe(video_path, youtube_url)
            
            # Step 3: Detect highlights using DeepSeek-R1
            self.logger.info("Step 3: Detecting highlights with AI...")
            highlights = self.highlight_detector.detect_highlights(
                transcript, 
                metadata,
                num_clips=self.config.max_clips_per_video
            )
            
            # Step 4: Extract video clips
            self.logger.info("Step 4: Extracting video clips...")
            clip_paths = []
            for i, highlight in enumerate(highlights):
                clip_path = self.clipper.extract_clip(
                    video_path,
                    highlight,
                    output_dir or self.config.output_dir,
                    clip_index=i
                )
                clip_paths.append(clip_path)

            # Step 5: Post-process clips with viral title enhancement
            self.logger.info("Step 5: Post-processing clips with viral enhancement...")
            enhanced_highlights = self._enhance_highlights_with_post_processor(
                clip_paths, highlights, metadata
            )

            # Step 6: Organize clips using YT Cutter structure
            self.logger.info("Step 6: Organizing clips...")
            clips_summary = self.viral_organizer.organize_clips(
                clip_paths,
                enhanced_highlights,  # Use enhanced highlights with viral titles
                metadata.get('title', 'Unknown Video'),
                youtube_url
            )

            # Extract final clip paths from the summary
            final_clips = [clip['file_path'] for clip in clips_summary.get('clips', [])]
            
            self.logger.info(f"Successfully generated {len(final_clips)} clips")
            return final_clips

        except Exception as e:
            self.logger.error(f"Error processing video {youtube_url}: {str(e)}")
            raise

    def _enhance_highlights_with_post_processor(
        self,
        clip_paths: List[str],
        highlights: List[Highlight],
        metadata: Dict[str, Any]
    ) -> List[Highlight]:
        """
        Enhance highlights with viral titles using post_processor.

        Args:
            clip_paths: List of clip file paths
            highlights: Original highlights from AI detection
            metadata: Video metadata

        Returns:
            Enhanced highlights with viral titles
        """
        enhanced_highlights = []

        for i, highlight in enumerate(highlights):
            try:
                # Generate enhanced viral title using post_processor AI
                enhanced_title = self.post_processor._generate_enhanced_title(highlight, metadata)

                # Create new highlight with enhanced title
                enhanced_highlight = Highlight(
                    start_time=highlight.start_time,
                    end_time=highlight.end_time,
                    title=enhanced_title,  # Use enhanced viral title
                    description=highlight.description,
                    confidence=highlight.confidence,
                    keywords=highlight.keywords,
                    viral_score=getattr(highlight, 'viral_score', 0),
                    viral_reasoning=getattr(highlight, 'viral_reasoning', ''),
                    suggested_platforms=getattr(highlight, 'suggested_platforms', []),
                    duration=highlight.duration
                )

                enhanced_highlights.append(enhanced_highlight)
                self.logger.info(f"Enhanced title for clip {i+1}: {enhanced_title}")

            except Exception as e:
                self.logger.warning(f"Failed to enhance title for clip {i+1}: {e}")
                # Fallback to original highlight
                enhanced_highlights.append(highlight)

        return enhanced_highlights
    
    def process_batch(self, urls_file: str, output_dir: Optional[str] = None) -> Dict[str, List[str]]:
        """
        Process multiple YouTube videos from a file.
        
        Args:
            urls_file: Path to file containing YouTube URLs (one per line)
            output_dir: Optional output directory for clips
            
        Returns:
            Dictionary mapping URLs to lists of generated clip paths
        """
        self.logger.info(f"Starting batch processing from: {urls_file}")
        
        with open(urls_file, 'r') as f:
            urls = [line.strip() for line in f if line.strip()]
        
        results = {}
        for i, url in enumerate(urls, 1):
            self.logger.info(f"Processing video {i}/{len(urls)}: {url}")
            try:
                clips = self.process_video(url, output_dir)
                results[url] = clips
            except Exception as e:
                self.logger.error(f"Failed to process {url}: {str(e)}")
                results[url] = []
        
        return results


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Clipper_Neon - AI-Powered YouTube Highlight Extraction"
    )
    
    parser.add_argument(
        "input",
        help="YouTube URL or path to file containing URLs"
    )
    
    parser.add_argument(
        "-o", "--output",
        help="Output directory for clips",
        default=None
    )
    
    parser.add_argument(
        "-c", "--config",
        help="Path to configuration file",
        default=None
    )
    
    parser.add_argument(
        "--batch",
        action="store_true",
        help="Process input as batch file containing multiple URLs"
    )
    
    parser.add_argument(
        "--max-clips",
        type=int,
        default=5,
        help="Maximum number of clips to extract per video"
    )
    
    args = parser.parse_args()
    
    # Initialize Clipper_Neon
    clipper = ClipperNeon(args.config)
    
    # Override max clips if specified
    if args.max_clips:
        clipper.config.max_clips_per_video = args.max_clips
    
    try:
        if args.batch:
            results = clipper.process_batch(args.input, args.output)
            print(f"\nBatch processing complete. Processed {len(results)} videos.")
            for url, clips in results.items():
                print(f"  {url}: {len(clips)} clips generated")
        else:
            clips = clipper.process_video(args.input, args.output)
            print(f"\nProcessing complete. Generated {len(clips)} clips:")
            for clip in clips:
                print(f"  - {clip}")
                
    except KeyboardInterrupt:
        print("\nProcessing interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nError: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
