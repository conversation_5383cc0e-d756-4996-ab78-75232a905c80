2025-08-06 20:16:35,180 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:16:37,210 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:16:37,210 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 20:16:37,211 - clipper_main - INFO - Starting processing for: https://youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:16:37,211 - clipper_main - INFO - Step 1: Downloading video...
2025-08-06 20:16:37,211 - video_downloader - INFO - Starting download for: https://youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:16:37,211 - video_downloader - INFO - Fetching video info for: https://youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:16:41,088 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 20:16:41,089 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 20:16:45,505 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 20:16:45,505 - clipper_main - INFO - Step 2: Generating transcript...
2025-08-06 20:16:46,416 - transcription_engine - INFO - Accessing YouTube transcript data: https://www.youtube.com/watch?v=https://youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:16:55,389 - transcription_engine - WARNING - No transcript data available for this video
2025-08-06 20:16:55,399 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 20:16:55,399 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 20:17:09,973 - highlight_detector - WARNING - Skipping invalid highlight data: float() argument must be a string or a real number, not 'NoneType'
2025-08-06 20:17:09,973 - highlight_detector - INFO - Parsed 0 highlights from AI response
2025-08-06 20:17:09,973 - highlight_detector - INFO - Raw highlights before filtering: 0
2025-08-06 20:17:09,973 - highlight_detector - INFO - Detected 0 valid highlights
2025-08-06 20:17:09,973 - clipper_main - INFO - Step 4: Extracting video clips...
2025-08-06 20:17:09,973 - clipper_main - INFO - Step 5: Post-processing clips...
2025-08-06 20:17:09,973 - post_processor - INFO - Post-processing 0 clips
2025-08-06 20:17:09,973 - clipper_main - ERROR - Error processing video https://youtube.com/watch?v=xKa_6kzOl0M: [WinError 3] The system cannot find the path specified: 'C:\\AI-Hub\\Applications\\Clipper_Neon\\data_out\\20250806_201709_we_broke_up..\\clips'
