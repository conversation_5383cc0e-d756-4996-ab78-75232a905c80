#!/usr/bin/env python3
"""
Setup Script for Clipper_Neon Automation
========================================

Installs and configures all dependencies for complete automation workflow.
"""

import subprocess
import sys
from pathlib import Path


def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"  ✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"  ❌ {description} failed: {e}")
        if e.stdout:
            print(f"     stdout: {e.stdout}")
        if e.stderr:
            print(f"     stderr: {e.stderr}")
        return False


def main():
    """Setup automation dependencies."""
    print("🚀 Clipper_Neon Automation Setup")
    print("=" * 50)
    
    # Core dependencies
    dependencies = [
        ("pip install youtube-transcript-api", "Installing YouTube Transcript API"),
        ("pip install playwright", "Installing Playwright browser automation"),
        ("pip install watchdog", "Installing file system watcher"),
        ("pip install requests", "Installing HTTP requests library"),
    ]
    
    # Install dependencies
    for cmd, desc in dependencies:
        if not run_command(cmd, desc):
            print(f"⚠️  Warning: {desc} failed, some features may not work")
    
    # Install Playwright browsers
    print("\n🌐 Installing browser engines...")
    browser_install = run_command("playwright install", "Installing Playwright browsers")
    
    if not browser_install:
        print("⚠️  Browser installation failed. Try running manually:")
        print("   playwright install")
    
    # Create automation directories
    print("\n📁 Creating automation directories...")
    
    project_root = Path(__file__).parent
    dirs_to_create = [
        "data_in/urls",
        "data_in/processed", 
        "data_out/daily_reports",
        "logs/automation",
        "configs/platforms"
    ]
    
    for dir_path in dirs_to_create:
        full_path = project_root / dir_path
        full_path.mkdir(parents=True, exist_ok=True)
        print(f"  ✅ Created: {dir_path}")
    
    # Create example configuration files
    print("\n⚙️  Creating example configurations...")
    
    # Example URL file
    example_urls = project_root / "data_in" / "example_urls.txt"
    with open(example_urls, 'w') as f:
        f.write("# Example YouTube URLs for processing\n")
        f.write("# Add one URL per line\n")
        f.write("# https://youtube.com/watch?v=VIDEO_ID_1\n")
        f.write("# https://youtube.com/watch?v=VIDEO_ID_2\n")
    
    print(f"  ✅ Created example URLs file: {example_urls}")
    
    # Platform configuration
    platform_config = {
        "youtube_shorts": {
            "enabled": True,
            "max_daily_uploads": 10,
            "title_max_length": 100,
            "description_max_length": 5000
        },
        "tiktok": {
            "enabled": True,
            "max_daily_uploads": 20,
            "caption_max_length": 300
        },
        "instagram": {
            "enabled": False,
            "max_daily_uploads": 15,
            "caption_max_length": 2200
        }
    }
    
    import json
    config_file = project_root / "configs" / "platforms" / "upload_config.json"
    with open(config_file, 'w') as f:
        json.dump(platform_config, f, indent=2)
    
    print(f"  ✅ Created platform config: {config_file}")
    
    # Create automation scripts
    print("\n📜 Creating automation scripts...")
    
    # Daily automation script
    daily_script = project_root / "run_daily.py"
    with open(daily_script, 'w') as f:
        f.write("""#!/usr/bin/env python3
\"\"\"
Daily Automation Runner
======================

Simple script to run daily automation with predefined settings.
\"\"\"

import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from daily_automation import DailyAutomation

def main():
    # URLs to process daily (modify as needed)
    daily_urls = [
        # Add your daily YouTube URLs here
        # "https://youtube.com/watch?v=VIDEO_ID"
    ]
    
    # Read URLs from file if it exists
    urls_file = Path(__file__).parent / "data_in" / "daily_urls.txt"
    if urls_file.exists():
        with open(urls_file, 'r') as f:
            file_urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        daily_urls.extend(file_urls)
    
    if not daily_urls:
        print("No URLs configured for daily processing")
        print(f"Add URLs to: {urls_file}")
        return
    
    # Run automation
    automation = DailyAutomation()
    automation.run_daily_workflow(daily_urls, platforms=['youtube_shorts', 'tiktok'])

if __name__ == "__main__":
    main()
""")
    
    print(f"  ✅ Created daily runner: {daily_script}")
    
    # Test script
    test_script = project_root / "test_automation.py"
    with open(test_script, 'w') as f:
        f.write("""#!/usr/bin/env python3
\"\"\"
Test Automation Setup
====================

Test script to verify automation components are working.
\"\"\"

import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

def test_imports():
    \"\"\"Test if all modules can be imported.\"\"\"
    print("🧪 Testing imports...")
    
    try:
        from config import ClipperConfig
        print("  ✅ Config module")
    except Exception as e:
        print(f"  ❌ Config module: {e}")
        return False
    
    try:
        from clipper_main import ClipperNeon
        print("  ✅ Main clipper")
    except Exception as e:
        print(f"  ❌ Main clipper: {e}")
        return False
    
    try:
        from auto_uploader import AutoUploader
        print("  ✅ Auto uploader")
    except Exception as e:
        print(f"  ❌ Auto uploader: {e}")
        return False
    
    try:
        from auto_processor import AutoProcessor
        print("  ✅ Auto processor")
    except Exception as e:
        print(f"  ❌ Auto processor: {e}")
        return False
    
    return True

def test_dependencies():
    \"\"\"Test external dependencies.\"\"\"
    print("\\n📦 Testing dependencies...")
    
    try:
        from youtube_transcript_api import YouTubeTranscriptApi
        print("  ✅ YouTube Transcript API")
    except ImportError:
        print("  ❌ YouTube Transcript API not available")
    
    try:
        from playwright.sync_api import sync_playwright
        print("  ✅ Playwright")
    except ImportError:
        print("  ❌ Playwright not available")
    
    try:
        from watchdog.observers import Observer
        print("  ✅ Watchdog")
    except ImportError:
        print("  ❌ Watchdog not available")

def main():
    print("🚀 Clipper_Neon Automation Test")
    print("=" * 40)
    
    if test_imports():
        print("\\n✅ All core modules imported successfully")
    else:
        print("\\n❌ Some core modules failed to import")
    
    test_dependencies()
    
    print("\\n🎉 Test completed!")

if __name__ == "__main__":
    main()
""")
    
    print(f"  ✅ Created test script: {test_script}")
    
    # Final instructions
    print("\n" + "=" * 50)
    print("🎉 Setup completed!")
    print("\n📋 Next steps:")
    print("1. Test the setup:")
    print("   python test_automation.py")
    print()
    print("2. Add YouTube URLs to process:")
    print(f"   Edit: {example_urls}")
    print()
    print("3. Run daily automation:")
    print("   python daily_automation.py data_in/example_urls.txt")
    print()
    print("4. For browser automation, you may need to:")
    print("   - Login to your social media accounts in the browser")
    print("   - Save login sessions for automated uploads")
    print()
    print("🚀 Your automation system is ready!")


if __name__ == "__main__":
    main()
