2025-08-06 21:17:06,055 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 21:17:08,081 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 21:17:08,083 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 21:17:10,123 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 21:17:10,124 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 21:17:10,124 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:17:10,124 - clipper_main - INFO - Step 1: Extracting transcript...
2025-08-06 21:17:10,495 - transcription_engine - INFO - Opening YouTube video: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:17:18,141 - transcription_engine - INFO - Expanding description with: tp-yt-paper-button#expand
2025-08-06 21:17:19,208 - transcription_engine - INFO - Found transcript button after expansion: text=Show transcript
2025-08-06 21:17:19,246 - transcription_engine - INFO - Waiting for transcript segments to load...
2025-08-06 21:17:20,284 - transcription_engine - INFO - Extracting transcript segments...
2025-08-06 21:17:22,994 - transcription_engine - INFO - [SUCCESS] Extracted 340 transcript segments via UI!
2025-08-06 21:17:23,005 - clipper_main - INFO - [SUCCESS] Transcript extracted: 340 segments
2025-08-06 21:17:23,005 - clipper_main - INFO - Step 2: Downloading video...
2025-08-06 21:17:23,005 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:17:23,006 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:17:27,105 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 21:17:27,105 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 21:17:32,427 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 21:17:32,427 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 21:17:32,427 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 21:18:31,510 - highlight_detector - ERROR - Failed to parse JSON response: Extra data: line 28 column 1 (char 863)
2025-08-06 21:18:31,510 - highlight_detector - ERROR - Raw response (first 500 chars): <think>
First, I need to understand the user's query. They provided a transcript of what appears to be a live stream or video content from someone who goes by N3onOnYT (N3on Singh), likely on platforms like TikTok or YouTube Shorts. The title and description both say "we broke up," which is vague, probably referring to a breakup topic.

The user wants me to generate the top 5 viral moments in this transcript, but they provided an example response format with only one highlight entry. I need to c
2025-08-06 21:18:31,510 - highlight_detector - ERROR - Cleaned JSON attempt: {
  "highlights": [
    {
      "start_time": 45.2,
      "end_time": 78.5,
      "title": "INSANE_Breakup_LIVE_She_EXPOSED_Everything_On",
      "description": "Viral moment description",
      "vira
2025-08-06 21:18:31,510 - highlight_detector - WARNING - Using fallback highlight detection
2025-08-06 21:18:31,510 - highlight_detector - INFO - Raw highlights before filtering: 3
2025-08-06 21:18:31,510 - highlight_detector - INFO - [OK] Accepted highlight: 'Highlight 1' (10.0s-55.0s) conf=0.60
2025-08-06 21:18:31,510 - highlight_detector - INFO - [OK] Accepted highlight: 'Highlight 2' (188.2s-233.2s) conf=0.60
2025-08-06 21:18:31,510 - highlight_detector - INFO - [OK] Accepted highlight: 'Highlight 3' (366.5s-411.5s) conf=0.60
2025-08-06 21:18:31,510 - highlight_detector - INFO - Detected 3 valid highlights
2025-08-06 21:18:31,511 - clipper_main - INFO - Step 4: Creating target folder and extracting clips...
2025-08-06 21:18:31,511 - clipper_main - INFO - Created target folder: 5. we broke up
2025-08-06 21:18:31,511 - video_clipper - INFO - Extracting clip 1: Highlight 1 (10.0s-55.0s)
2025-08-06 21:18:39,939 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\5. we broke up\clip_01_Highlight_1.mp4 (9.9 MB)
2025-08-06 21:18:39,939 - video_clipper - INFO - Extracting clip 2: Highlight 2 (188.2s-233.2s)
2025-08-06 21:18:54,681 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\5. we broke up\clip_02_Highlight_2.mp4 (12.6 MB)
2025-08-06 21:18:54,682 - video_clipper - INFO - Extracting clip 3: Highlight 3 (366.5s-411.5s)
2025-08-06 21:19:15,199 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\5. we broke up\clip_03_Highlight_3.mp4 (12.7 MB)
2025-08-06 21:19:15,200 - clipper_main - INFO - Step 5: Post-processing clips with viral enhancement...
2025-08-06 21:19:20,143 - clipper_main - INFO - Enhanced title for clip 1: INSANE_AI_Generated_Viral_Content_Ready_For
2025-08-06 21:19:24,872 - clipper_main - INFO - Enhanced title for clip 2: INSANE_AI_Generated_Viral_Content_Ready_For
2025-08-06 21:19:29,645 - clipper_main - INFO - Enhanced title for clip 3: INSANE_AI_Generated_Viral_Content_Ready_For
2025-08-06 21:19:29,645 - clipper_main - INFO - Step 6: Generating clips metadata...
2025-08-06 21:19:29,645 - clipper_main - INFO - Generated clips metadata: C:\AI-Hub\Applications\Clipper_Neon\data_out\5. we broke up\clips_info.json
2025-08-06 21:19:29,645 - clipper_main - INFO - Successfully generated 3 clips
