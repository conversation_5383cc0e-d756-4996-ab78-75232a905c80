"""
Highlight Detection Module for Clipper_Neon
===========================================

Uses DeepSeek-R1 via Ollama to intelligently detect viral-worthy moments
from video transcripts.
"""

import json
import logging
import requests
from typing import List, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class Highlight:
    """Enhanced data class for a viral-optimized highlight."""
    start_time: float
    end_time: float
    title: str
    description: str
    confidence: float
    keywords: List[str]

    # V.I.R.A.L. Framework Components
    viral_components: Dict[str, str] = None
    viral_scores: Dict[str, float] = None
    suggested_platforms: List[str] = None
    viral_reasoning: str = ""

    def __post_init__(self):
        """Initialize default values for viral components."""
        if self.viral_components is None:
            self.viral_components = {}
        if self.viral_scores is None:
            self.viral_scores = {
                "hook_strength": 5.0,
                "retention_power": 5.0,
                "share_potential": 5.0,
                "cognitive_load": 5.0,
                "viral_score": 25.0
            }
        if self.suggested_platforms is None:
            self.suggested_platforms = ["youtube_shorts", "tiktok"]

    @property
    def duration(self) -> float:
        """Get the duration of the highlight in seconds."""
        return self.end_time - self.start_time

    @property
    def viral_score(self) -> float:
        """Calculate the viral score using the formula: (Hook × Retention × Share) ÷ Cognitive Load."""
        scores = self.viral_scores
        hook = scores.get("hook_strength", 5.0)
        retention = scores.get("retention_power", 5.0)
        share = scores.get("share_potential", 5.0)
        cognitive_load = max(scores.get("cognitive_load", 5.0), 1.0)  # Prevent division by zero

        return (hook * retention * share) / cognitive_load


class HighlightDetector:
    """Detects viral-worthy highlights using DeepSeek-R1."""
    
    def __init__(self, config):
        """Initialize the highlight detector."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.ollama_url = f"{self.config.ollama_host}/api/generate"
        
        # Test Ollama connection
        self._test_ollama_connection()
    
    def _test_ollama_connection(self):
        """Test connection to Ollama service."""
        try:
            response = requests.get(f"{self.config.ollama_host}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [model['name'] for model in models]
                
                if self.config.highlight_detection_model not in model_names:
                    self.logger.warning(
                        f"Model {self.config.highlight_detection_model} not found. "
                        f"Available models: {model_names}"
                    )
                else:
                    self.logger.info(f"Connected to Ollama. Using model: {self.config.highlight_detection_model}")
            else:
                raise ConnectionError(f"Ollama returned status {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"Failed to connect to Ollama at {self.config.ollama_host}: {e}")
            raise
    
    def detect_highlights(
        self, 
        transcript: str, 
        metadata: Dict[str, Any], 
        num_clips: Optional[int] = None
    ) -> List[Highlight]:
        """
        Detect highlights from video transcript using DeepSeek-R1.
        
        Args:
            transcript: Video transcript text
            metadata: Video metadata dictionary
            num_clips: Number of clips to generate (uses config default if None)
            
        Returns:
            List of Highlight objects sorted by confidence
        """
        if num_clips is None:
            num_clips = self.config.max_clips_per_video
        
        self.logger.info(f"Detecting highlights using {self.config.highlight_detection_model}")
        
        # Prepare the prompt
        prompt = self._build_highlight_prompt(transcript, metadata, num_clips)
        
        # Query DeepSeek-R1
        response = self._query_ollama(prompt)
        
        # Parse response and create Highlight objects
        highlights = self._parse_highlights_response(response, metadata)
        
        # Filter and sort highlights
        filtered_highlights = self._filter_highlights(highlights)
        
        self.logger.info(f"Detected {len(filtered_highlights)} valid highlights")
        return filtered_highlights[:num_clips]
    
    def _build_highlight_prompt(self, transcript: str, metadata: Dict[str, Any], num_clips: int) -> str:
        """Build the prompt for highlight detection."""
        # Get the prompt template from config
        prompt_template = self.config.get_highlight_prompt()
        
        # Format the prompt with actual data
        formatted_prompt = prompt_template.format(
            max_clips=num_clips,
            transcript=transcript,
            title=metadata.get('title', 'Unknown'),
            duration=metadata.get('duration', 0),
            description=metadata.get('description', '')[:500]  # Limit description length
        )
        
        return formatted_prompt
    
    def _query_ollama(self, prompt: str) -> str:
        """Query Ollama API with the highlight detection prompt."""
        payload = {
            "model": self.config.highlight_detection_model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.3,  # Lower temperature for more consistent results
                "top_p": 0.9,
                "num_predict": 2000  # Limit response length
            }
        }
        
        try:
            self.logger.debug("Sending request to Ollama...")
            response = requests.post(
                self.ollama_url,
                json=payload,
                timeout=120  # 2 minute timeout for LLM processing
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')
            else:
                raise Exception(f"Ollama API error: {response.status_code} - {response.text}")
                
        except requests.exceptions.Timeout:
            self.logger.error("Ollama request timed out")
            raise
        except Exception as e:
            self.logger.error(f"Error querying Ollama: {e}")
            raise
    
    def _parse_highlights_response(self, response: str, metadata: Dict[str, Any]) -> List[Highlight]:
        """Parse the JSON response from DeepSeek-R1 into Highlight objects."""
        highlights = []
        
        try:
            # Try to extract JSON from the response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start == -1 or json_end == 0:
                raise ValueError("No JSON found in response")
            
            json_str = response[json_start:json_end]
            data = json.loads(json_str)
            
            # Extract highlights from the response
            highlights_data = data.get('highlights', [])
            
            for highlight_data in highlights_data:
                try:
                    highlight = Highlight(
                        start_time=float(highlight_data.get('start_time', 0)),
                        end_time=float(highlight_data.get('end_time', 0)),
                        title=str(highlight_data.get('title', '')),
                        description=str(highlight_data.get('description', '')),
                        confidence=float(highlight_data.get('confidence', 0.5)),
                        keywords=highlight_data.get('keywords', []),
                        viral_components=highlight_data.get('viral_components', {}),
                        viral_scores=highlight_data.get('viral_scores', {}),
                        suggested_platforms=highlight_data.get('suggested_platforms', ["youtube_shorts", "tiktok"]),
                        viral_reasoning=str(highlight_data.get('viral_reasoning', ''))
                    )
                    highlights.append(highlight)

                except (ValueError, TypeError) as e:
                    self.logger.warning(f"Skipping invalid highlight data: {e}")
                    continue
            
            self.logger.info(f"Parsed {len(highlights)} highlights from AI response")
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON response: {e}")
            self.logger.debug(f"Raw response: {response}")
            
            # Fallback: create highlights based on transcript analysis
            highlights = self._fallback_highlight_detection(metadata)
        
        except Exception as e:
            self.logger.error(f"Error parsing highlights: {e}")
            highlights = self._fallback_highlight_detection(metadata)

        # Debug: Log all parsed highlights before filtering
        self.logger.info(f"Raw highlights before filtering: {len(highlights)}")
        for i, h in enumerate(highlights):
            self.logger.debug(f"  {i+1}. {h.title} ({h.start_time:.1f}s-{h.end_time:.1f}s) conf={h.confidence:.2f}")

        return highlights
    
    def _filter_highlights(self, highlights: List[Highlight]) -> List[Highlight]:
        """Filter highlights based on configuration criteria."""
        filtered = []
        
        for highlight in highlights:
            # Check confidence threshold
            if highlight.confidence < self.config.highlight_confidence_threshold:
                self.logger.info(f"Skipping low confidence highlight: '{highlight.title}' conf={highlight.confidence:.2f} (threshold={self.config.highlight_confidence_threshold})")
                continue

            # Check duration constraints
            if highlight.duration < self.config.min_clip_duration:
                self.logger.info(f"Skipping short highlight: '{highlight.title}' duration={highlight.duration:.1f}s (min={self.config.min_clip_duration}s)")
                continue

            if highlight.duration > self.config.max_clip_duration:
                self.logger.info(f"Skipping long highlight: '{highlight.title}' duration={highlight.duration:.1f}s (max={self.config.max_clip_duration}s)")
                continue

            # Check for valid timestamps
            if highlight.start_time < 0 or highlight.end_time <= highlight.start_time:
                self.logger.info(f"Skipping invalid timestamps: '{highlight.title}' {highlight.start_time:.1f}s-{highlight.end_time:.1f}s")
                continue

            self.logger.info(f"[OK] Accepted highlight: '{highlight.title}' ({highlight.start_time:.1f}s-{highlight.end_time:.1f}s) conf={highlight.confidence:.2f}")
            filtered.append(highlight)
        
        # Sort by confidence (highest first)
        filtered.sort(key=lambda h: h.confidence, reverse=True)
        
        return filtered
    
    def _fallback_highlight_detection(self, metadata: Dict[str, Any]) -> List[Highlight]:
        """Fallback method when AI detection fails."""
        self.logger.warning("Using fallback highlight detection")
        
        duration = metadata.get('duration', 300)  # Default 5 minutes
        
        # Create simple highlights based on video duration
        highlights = []
        
        if duration > 60:  # Only create highlights for videos longer than 1 minute
            # Create highlights at strategic points
            segment_length = min(45, duration / 4)  # Max 45 second segments
            
            for i in range(min(3, int(duration / 60))):  # Max 3 highlights
                start_time = (duration / 4) * i + 10  # Start after intro
                end_time = min(start_time + segment_length, duration - 10)  # End before outro
                
                if end_time > start_time + self.config.min_clip_duration:
                    highlight = Highlight(
                        start_time=start_time,
                        end_time=end_time,
                        title=f"Highlight {i+1}",
                        description="Auto-generated highlight",
                        confidence=0.6,
                        keywords=["auto-generated"]
                    )
                    highlights.append(highlight)
        
        return highlights


if __name__ == "__main__":
    # Test the highlight detector
    from config import ClipperConfig
    
    config = ClipperConfig()
    detector = HighlightDetector(config)
    
    # Test with sample data
    sample_transcript = "This is a sample transcript with some interesting content..."
    sample_metadata = {
        'title': 'Test Video',
        'duration': 300,
        'description': 'A test video for highlight detection'
    }
    
    try:
        highlights = detector.detect_highlights(sample_transcript, sample_metadata, 3)
        print(f"Detected {len(highlights)} highlights:")
        for i, highlight in enumerate(highlights, 1):
            print(f"{i}. {highlight.title} ({highlight.start_time:.1f}s-{highlight.end_time:.1f}s)")
    except Exception as e:
        print(f"Error: {e}")
