2025-08-06 20:47:50,319 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 20:47:52,380 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:47:52,381 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 20:47:54,417 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:47:54,418 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 20:47:54,418 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:47:54,419 - clipper_main - INFO - Step 1: Extracting transcript...
2025-08-06 20:47:54,787 - transcription_engine - INFO - Accessing YouTube transcript data: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:48:01,768 - transcription_engine - WARNING - No transcript data available for this video
2025-08-06 20:48:01,792 - transcription_engine - INFO - Trying youtube-transcript-api for video: xKa_6kzOl0M
2025-08-06 20:48:01,792 - transcription_engine - WARNING - youtube-transcript-api: No transcript found in any language
2025-08-06 20:48:01,794 - clipper_main - INFO - Step 2: Downloading video...
2025-08-06 20:48:01,794 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:48:01,794 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:48:05,635 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 20:48:05,636 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 20:48:10,427 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 20:48:10,428 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 20:48:10,429 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
