2025-08-06 20:42:06,115 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 20:42:08,137 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:42:08,138 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 20:42:10,188 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:42:10,188 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 20:42:10,189 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:42:10,190 - clipper_main - INFO - Step 1: Downloading video...
2025-08-06 20:42:10,190 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:42:10,190 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:42:14,243 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 20:42:14,244 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 20:42:19,015 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 20:42:19,016 - clipper_main - INFO - Step 2: Generating transcript...
2025-08-06 20:42:19,394 - transcription_engine - INFO - Accessing YouTube transcript data: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:42:31,540 - transcription_engine - WARNING - No transcript data available for this video
2025-08-06 20:42:31,547 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 20:42:31,547 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 20:43:27,570 - highlight_detector - ERROR - Failed to parse JSON response: Expecting ',' delimiter: line 94 column 6 (char 3887)
2025-08-06 20:43:27,570 - highlight_detector - WARNING - Using fallback highlight detection
2025-08-06 20:43:27,571 - highlight_detector - INFO - Raw highlights before filtering: 3
2025-08-06 20:43:27,575 - highlight_detector - INFO - Detected 3 valid highlights
2025-08-06 20:43:27,575 - clipper_main - INFO - Step 4: Extracting video clips...
2025-08-06 20:43:27,575 - video_clipper - INFO - Extracting clip 1: Highlight 1 (10.0s-55.0s)
2025-08-06 20:43:35,371 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_01_Highlight_1.mp4 (9.9 MB)
2025-08-06 20:43:35,372 - video_clipper - INFO - Extracting clip 2: Highlight 2 (188.2s-233.2s)
2025-08-06 20:43:49,548 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_02_Highlight_2.mp4 (12.6 MB)
2025-08-06 20:43:49,548 - video_clipper - INFO - Extracting clip 3: Highlight 3 (366.5s-411.5s)
2025-08-06 20:44:09,491 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_03_Highlight_3.mp4 (12.7 MB)
2025-08-06 20:44:09,491 - clipper_main - INFO - Step 5: Post-processing clips with viral enhancement...
2025-08-06 20:44:14,275 - clipper_main - WARNING - Failed to enhance title for clip 1: Highlight.__init__() got an unexpected keyword argument 'viral_score'
2025-08-06 20:44:18,994 - clipper_main - WARNING - Failed to enhance title for clip 2: Highlight.__init__() got an unexpected keyword argument 'viral_score'
2025-08-06 20:44:23,717 - clipper_main - WARNING - Failed to enhance title for clip 3: Highlight.__init__() got an unexpected keyword argument 'viral_score'
2025-08-06 20:44:23,717 - clipper_main - INFO - Step 6: Organizing clips...
2025-08-06 20:44:23,734 - clipper_main - INFO - Successfully generated 3 clips
