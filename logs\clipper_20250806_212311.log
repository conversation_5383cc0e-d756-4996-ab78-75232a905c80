2025-08-06 21:23:11,062 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 21:23:13,091 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 21:23:13,092 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 21:23:15,147 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 21:23:15,147 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 21:23:15,148 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:23:15,148 - clipper_main - INFO - Step 1: Extracting transcript...
2025-08-06 21:23:15,522 - transcription_engine - INFO - Opening YouTube video: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:23:24,584 - transcription_engine - INFO - Expanding description with: tp-yt-paper-button#expand
2025-08-06 21:23:25,659 - transcription_engine - INFO - Found transcript button after expansion: text=Show transcript
2025-08-06 21:23:25,699 - transcription_engine - INFO - Waiting for transcript segments to load...
2025-08-06 21:23:26,721 - transcription_engine - INFO - Extracting transcript segments...
2025-08-06 21:23:29,649 - transcription_engine - INFO - [SUCCESS] Extracted 340 transcript segments via UI!
2025-08-06 21:23:29,662 - clipper_main - INFO - [SUCCESS] Transcript extracted: 340 segments
2025-08-06 21:23:29,662 - clipper_main - INFO - Step 2: Downloading video...
2025-08-06 21:23:29,662 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:23:29,663 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:23:34,129 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 21:23:34,129 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 21:23:38,870 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 21:23:38,870 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 21:23:38,870 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 21:24:37,550 - highlight_detector - ERROR - Failed to parse JSON response: Expecting ',' delimiter: line 4 column 25 (char 65)
2025-08-06 21:24:37,550 - highlight_detector - ERROR - Raw response (first 500 chars): <think>
Hmm, the user's query is about a person who breaks up with someone and then gets back together with his ex-girlfriend because they are too afraid to leave them behind. But I'm not sure if she will come over or not.", 
        'end': 78.5,
        "viral_level": 0, "hook_strength": 92}

I need to write a Python function that takes in the user's query and extracts information about viral content", end_time: The above is an example of how I thought but it was wrong.

Okay, let me think step
2025-08-06 21:24:37,550 - highlight_detector - ERROR - Cleaned JSON attempt: {
  "id": "unique_id",
  "end_time": 79,
  "title": "SHEINMAN'S "BLOG": A Journey Through The Wonders Of Breakups - From Heartbreak to Healing and Rebuilding", 
    "description": "A short description
2025-08-06 21:24:37,550 - highlight_detector - WARNING - Using fallback highlight detection
2025-08-06 21:24:37,551 - highlight_detector - INFO - Raw highlights before filtering: 3
2025-08-06 21:24:37,551 - highlight_detector - INFO - [OK] Accepted highlight: 'Highlight 1' (10.0s-55.0s) conf=0.60
2025-08-06 21:24:37,551 - highlight_detector - INFO - [OK] Accepted highlight: 'Highlight 2' (188.2s-233.2s) conf=0.60
2025-08-06 21:24:37,551 - highlight_detector - INFO - [OK] Accepted highlight: 'Highlight 3' (366.5s-411.5s) conf=0.60
2025-08-06 21:24:37,551 - highlight_detector - INFO - Detected 3 valid highlights
2025-08-06 21:24:37,551 - clipper_main - INFO - Step 4: Creating target folder and extracting clips...
2025-08-06 21:24:37,552 - clipper_main - INFO - Created target folder: 7. we broke up
2025-08-06 21:24:37,552 - video_clipper - INFO - Extracting clip 1: Highlight 1 (10.0s-55.0s)
2025-08-06 21:24:45,882 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\7. we broke up\clip_01_Highlight_1.mp4 (9.9 MB)
2025-08-06 21:24:45,882 - video_clipper - INFO - Extracting clip 2: Highlight 2 (188.2s-233.2s)
