2025-08-06 20:18:04,849 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 20:18:06,904 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:18:06,905 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 20:18:08,921 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 20:18:08,922 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 20:18:08,922 - clipper_main - INFO - Starting processing for: https://youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:18:08,922 - clipper_main - INFO - Step 1: Downloading video...
2025-08-06 20:18:08,922 - video_downloader - INFO - Starting download for: https://youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:18:08,922 - video_downloader - INFO - Fetching video info for: https://youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:18:13,086 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 20:18:13,087 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 20:18:17,729 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 20:18:17,729 - clipper_main - INFO - Step 2: Generating transcript...
2025-08-06 20:18:18,105 - transcription_engine - INFO - Accessing YouTube transcript data: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 20:18:25,199 - transcription_engine - WARNING - No transcript data available for this video
2025-08-06 20:18:25,205 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 20:18:25,205 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 20:18:41,114 - highlight_detector - INFO - Parsed 1 highlights from AI response
2025-08-06 20:18:41,114 - highlight_detector - INFO - Raw highlights before filtering: 1
2025-08-06 20:18:41,116 - highlight_detector - INFO - Detected 1 valid highlights
2025-08-06 20:18:41,117 - clipper_main - INFO - Step 4: Extracting video clips...
2025-08-06 20:18:41,117 - video_clipper - INFO - Extracting clip 1: INSANE_Breakup_LIVE_She_EXPOSED_Everything_On (45.2s-78.5s)
2025-08-06 20:18:47,412 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_01_INSANE_Breakup_LIVE_She_EXPOSED_Everything_On.mp4 (6.1 MB)
2025-08-06 20:18:47,412 - clipper_main - INFO - Step 5: Post-processing clips...
2025-08-06 20:18:47,412 - post_processor - INFO - Post-processing 1 clips
2025-08-06 20:18:47,412 - clipper_main - ERROR - Error processing video https://youtube.com/watch?v=xKa_6kzOl0M: [WinError 3] The system cannot find the path specified: 'C:\\AI-Hub\\Applications\\Clipper_Neon\\data_out\\20250806_201847_we_broke_up..\\clips'
