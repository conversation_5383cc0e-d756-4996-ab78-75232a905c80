2025-08-06 19:14:09,805 - transcription_engine - INFO - YouTube Transcript API available for perfect timestamps
2025-08-06 19:14:10,896 - transcription_engine - INFO - Whisper is available for transcription fallback
2025-08-06 19:14:12,932 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 19:14:12,932 - transcription_engine - INFO - YouTube Transcript API available for perfect timestamps
2025-08-06 19:14:12,932 - transcription_engine - INFO - Whisper is available for transcription fallback
2025-08-06 19:14:14,948 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 19:14:14,948 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 19:14:14,949 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 19:14:14,949 - clipper_main - INFO - Step 1: Downloading video...
2025-08-06 19:14:14,949 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 19:14:14,949 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 19:14:19,062 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 19:14:19,062 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 19:16:39,963 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 19:16:39,964 - clipper_main - INFO - Step 2: Generating transcript...
2025-08-06 19:16:39,964 - transcription_engine - INFO - Starting transcription for: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 19:16:39,964 - transcription_engine - INFO - Fetching YouTube transcript for video ID: xKa_6kzOl0M
2025-08-06 19:16:39,964 - transcription_engine - WARNING - YouTube transcript extraction failed: type object 'YouTubeTranscriptApi' has no attribute 'get_transcript'
2025-08-06 19:16:39,964 - transcription_engine - INFO - Extracting audio from video...
2025-08-06 19:16:40,364 - transcription_engine - INFO - Audio extracted: C:\Users\<USER>\AppData\Local\Temp\tmptic4b8o0.wav (21.7 MB)
2025-08-06 19:16:40,364 - transcription_engine - INFO - Loading Whisper model...
2025-08-06 19:16:40,811 - transcription_engine - ERROR - Whisper transcription failed: Numpy is not available
2025-08-06 19:16:40,811 - transcription_engine - WARNING - Using fallback transcription method
2025-08-06 19:16:40,812 - transcription_engine - WARNING - speech_recognition library not available
2025-08-06 19:16:40,812 - transcription_engine - WARNING - No transcription method available. Using placeholder.
2025-08-06 19:16:40,814 - transcription_engine - INFO - Transcription completed. Length: 76 characters
2025-08-06 19:16:40,816 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 19:16:40,816 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 19:16:59,487 - highlight_detector - ERROR - Failed to parse JSON response: Expecting property name enclosed in double quotes: line 9 column 9 (char 249)
2025-08-06 19:16:59,487 - highlight_detector - WARNING - Using fallback highlight detection
2025-08-06 19:16:59,487 - highlight_detector - INFO - Raw highlights before filtering: 3
2025-08-06 19:16:59,491 - highlight_detector - INFO - Detected 3 valid highlights
2025-08-06 19:16:59,491 - clipper_main - INFO - Step 4: Extracting video clips...
2025-08-06 19:16:59,491 - video_clipper - INFO - Extracting clip 1: Highlight 1 (10.0s-55.0s)
2025-08-06 19:17:08,935 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_01_Highlight_1.mp4 (9.9 MB)
2025-08-06 19:17:08,935 - video_clipper - INFO - Extracting clip 2: Highlight 2 (188.2s-233.2s)
2025-08-06 19:17:24,712 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_02_Highlight_2.mp4 (12.6 MB)
2025-08-06 19:17:24,712 - video_clipper - INFO - Extracting clip 3: Highlight 3 (366.5s-411.5s)
2025-08-06 19:17:46,990 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_03_Highlight_3.mp4 (12.7 MB)
2025-08-06 19:17:46,990 - clipper_main - INFO - Step 5: Post-processing clips...
2025-08-06 19:17:46,990 - post_processor - INFO - Post-processing 3 clips
2025-08-06 19:17:46,990 - clipper_main - ERROR - Error processing video https://www.youtube.com/watch?v=xKa_6kzOl0M: [WinError 3] The system cannot find the path specified: 'C:\\AI-Hub\\Applications\\Clipper_Neon\\data_out\\20250806_191746_we_broke_up..\\clips'
