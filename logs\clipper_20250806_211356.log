2025-08-06 21:13:56,940 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 21:13:58,972 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 21:13:58,974 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 21:14:01,005 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 21:14:01,005 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 21:14:01,006 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:14:01,007 - clipper_main - INFO - Step 1: Extracting transcript...
2025-08-06 21:14:01,393 - transcription_engine - INFO - Opening YouTube video: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:14:12,235 - transcription_engine - INFO - Expanding description with: tp-yt-paper-button#expand
2025-08-06 21:14:13,294 - transcription_engine - INFO - Found transcript button after expansion: text=Show transcript
2025-08-06 21:14:13,337 - transcription_engine - INFO - Waiting for transcript segments to load...
2025-08-06 21:14:14,064 - transcription_engine - INFO - Extracting transcript segments...
2025-08-06 21:14:16,805 - transcription_engine - INFO - [SUCCESS] Extracted 340 transcript segments via UI!
2025-08-06 21:14:16,815 - clipper_main - INFO - [SUCCESS] Transcript extracted: 340 segments
2025-08-06 21:14:16,815 - clipper_main - INFO - Step 2: Downloading video...
2025-08-06 21:14:16,815 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:14:16,816 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:14:21,105 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 21:14:21,105 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 21:14:25,642 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 21:14:25,643 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 21:14:25,643 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 21:15:25,807 - highlight_detector - ERROR - Failed to parse JSON response: Expecting value: line 1 column 26 (char 25)
2025-08-06 21:15:25,807 - highlight_detector - WARNING - Using fallback highlight detection
2025-08-06 21:15:25,807 - highlight_detector - INFO - Raw highlights before filtering: 3
2025-08-06 21:15:25,808 - highlight_detector - INFO - [OK] Accepted highlight: 'Highlight 1' (10.0s-55.0s) conf=0.60
2025-08-06 21:15:25,808 - highlight_detector - INFO - [OK] Accepted highlight: 'Highlight 2' (188.2s-233.2s) conf=0.60
2025-08-06 21:15:25,808 - highlight_detector - INFO - [OK] Accepted highlight: 'Highlight 3' (366.5s-411.5s) conf=0.60
2025-08-06 21:15:25,808 - highlight_detector - INFO - Detected 3 valid highlights
2025-08-06 21:15:25,808 - clipper_main - INFO - Step 4: Creating target folder and extracting clips...
2025-08-06 21:15:25,809 - clipper_main - INFO - Created target folder: 4. we broke up
2025-08-06 21:15:25,809 - video_clipper - INFO - Extracting clip 1: Highlight 1 (10.0s-55.0s)
2025-08-06 21:15:33,395 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\4. we broke up\clip_01_Highlight_1.mp4 (9.9 MB)
2025-08-06 21:15:33,395 - video_clipper - INFO - Extracting clip 2: Highlight 2 (188.2s-233.2s)
2025-08-06 21:15:47,199 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\4. we broke up\clip_02_Highlight_2.mp4 (12.6 MB)
2025-08-06 21:15:47,199 - video_clipper - INFO - Extracting clip 3: Highlight 3 (366.5s-411.5s)
2025-08-06 21:16:06,735 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\4. we broke up\clip_03_Highlight_3.mp4 (12.7 MB)
2025-08-06 21:16:06,735 - clipper_main - INFO - Step 5: Post-processing clips with viral enhancement...
2025-08-06 21:16:11,534 - clipper_main - INFO - Enhanced title for clip 1: INSANE_AI_Generated_Viral_Content_Ready_For
2025-08-06 21:16:16,215 - clipper_main - INFO - Enhanced title for clip 2: INSANE_AI_Generated_Viral_Content_Ready_For
2025-08-06 21:16:20,862 - clipper_main - INFO - Enhanced title for clip 3: INSANE_AI_Generated_Viral_Content_Ready_For
2025-08-06 21:16:20,862 - clipper_main - INFO - Step 6: Generating clips metadata...
2025-08-06 21:16:20,862 - clipper_main - INFO - Generated clips metadata: C:\AI-Hub\Applications\Clipper_Neon\data_out\4. we broke up\clips_info.json
2025-08-06 21:16:20,862 - clipper_main - INFO - Successfully generated 3 clips
