"""
Post-Processor Module for Clipper_Neon
======================================

Handles post-processing of extracted clips including title generation,
file organization, and metadata creation.
"""

import json
import logging
import shutil
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

from highlight_detector import Highlight


class PostProcessor:
    """Handles post-processing of extracted video clips."""
    
    def __init__(self, config):
        """Initialize the post-processor."""
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def process_clips(
        self,
        clip_paths: List[str],
        highlights: List[Highlight],
        metadata: Dict[str, Any]
    ) -> List[str]:
        """
        Post-process extracted clips.
        
        Args:
            clip_paths: List of paths to extracted clip files
            highlights: List of Highlight objects corresponding to clips
            metadata: Original video metadata
            
        Returns:
            List of paths to final processed clips
        """
        self.logger.info(f"Post-processing {len(clip_paths)} clips")
        
        processed_clips = []
        
        # Create organized output structure
        output_dir = self._create_output_structure(metadata)
        
        for i, (clip_path, highlight) in enumerate(zip(clip_paths, highlights)):
            try:
                # Process individual clip
                processed_clip = self._process_single_clip(
                    clip_path, 
                    highlight, 
                    metadata, 
                    output_dir, 
                    i
                )
                processed_clips.append(processed_clip)
                
            except Exception as e:
                self.logger.error(f"Failed to process clip {i + 1}: {e}")
                continue
        
        # Generate summary report
        self._generate_summary_report(processed_clips, highlights, metadata, output_dir)
        
        self.logger.info(f"Post-processing completed. {len(processed_clips)} clips ready")
        return processed_clips
    
    def _create_output_structure(self, metadata: Dict[str, Any]) -> Path:
        """Create organized output directory structure."""
        # Create main output directory based on video title and date
        safe_title = self._sanitize_filename(metadata.get('title', 'Unknown_Video'))
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        output_dir = Path(self.config.output_dir) / f"{timestamp}_{safe_title}"
        
        # Create subdirectories
        subdirs = ['clips', 'thumbnails', 'metadata', 'transcripts']
        for subdir in subdirs:
            (output_dir / subdir).mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"Created output structure: {output_dir}")
        return output_dir
    
    def _process_single_clip(
        self,
        clip_path: str,
        highlight: Highlight,
        metadata: Dict[str, Any],
        output_dir: Path,
        clip_index: int
    ) -> str:
        """Process a single clip."""
        clip_path = Path(clip_path)
        
        # Generate enhanced title using AI if available
        enhanced_title = self._generate_enhanced_title(highlight, metadata)
        
        # Create viral-optimized filename (platform-ready)
        viral_filename = self._create_viral_filename(enhanced_title, clip_index)
        final_path = output_dir / viral_filename
        
        # Copy/move clip to final location
        shutil.copy2(clip_path, final_path)
        
        # Create clip metadata
        clip_metadata = self._create_clip_metadata(
            highlight, 
            metadata, 
            enhanced_title, 
            final_path
        )
        
        # Save clip metadata
        metadata_file = output_dir / 'metadata' / f"clip_{clip_index + 1:02d}_metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(clip_metadata, f, indent=2, ensure_ascii=False)
        
        # Generate social media captions if enabled
        if self.config.include_captions:
            self._generate_social_captions(clip_metadata, output_dir, clip_index)
        
        self.logger.info(f"Processed clip: {final_path}")
        return str(final_path)

    def _create_viral_filename(self, viral_title: str, clip_index: int) -> str:
        """Create viral-optimized filename that's platform-ready for upload."""
        # Format: "01_VIRAL_TITLE.mp4" (no extra text, direct upload ready)
        clip_number = f"{clip_index + 1:02d}"

        # Clean the viral title (should already be optimized from AI)
        clean_title = viral_title.replace(' ', '_').replace('-', '_')

        # Remove any problematic characters for filenames
        import re
        clean_title = re.sub(r'[<>:"/\\|?*]', '', clean_title)

        # Ensure it's not too long for filesystem (keep under 200 chars total)
        if len(clean_title) > 180:
            clean_title = clean_title[:180]

        return f"{clip_number}_{clean_title}.{self.config.clip_format}"

    def _generate_enhanced_title(self, highlight: Highlight, metadata: Dict[str, Any]) -> str:
        """Generate enhanced title using AI or fallback to original."""
        try:
            # Try to use DeepSeek-R1 for title enhancement
            enhanced_title = self._ai_enhance_title(highlight, metadata)
            if enhanced_title and len(enhanced_title.strip()) > 0:
                return enhanced_title
        except Exception as e:
            self.logger.warning(f"AI title enhancement failed: {e}")
        
        # Fallback to original title
        return highlight.title
    
    def _ai_enhance_title(self, highlight: Highlight, metadata: Dict[str, Any]) -> str:
        """Use AI to enhance clip title for viral potential."""
        try:
            import requests
            
            prompt = f"""
Create a VIRAL-OPTIMIZED title using the proven viral framework:

Original Title: {highlight.title}
Description: {highlight.description}
Keywords: {', '.join(highlight.keywords)}
Video Context: {metadata.get('title', '')}
Viral Score: {getattr(highlight, 'viral_score', 0)}

VIRAL TITLE REQUIREMENTS:
1. Start with EMOTIONAL HOOK: PSYCHO, INSANE, SHOCKING, WILD, CRAZY, UNBELIEVABLE
2. Include SPECIFIC STAKES: Dollar amounts, consequences, authority figures
3. Add DRAMA ELEMENTS: EXPOSED, LEAKED, FIRED, ARRESTED, SWAT, CARTEL
4. Strategic TRUNCATION: Cut at 45-50 characters for cliffhanger effect
5. Use UNDERSCORES instead of spaces (platform-ready)
6. NO quotation marks, colons, or special characters

EXAMPLES:
- "PSYCHO_Assistant_STOLE_60K_Got_SWAT_Raided_Af"
- "INSANE_Breakup_LIVE_She_EXPOSED_Everything_On"
- "CEO_FIRED_Me_For_This_Video_LEAKED_Internal_Em"

Return ONLY the viral title with underscores, nothing else.
"""
            
            payload = {
                "model": self.config.highlight_detection_model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "num_predict": 100
                }
            }
            
            response = requests.post(
                f"{self.config.ollama_host}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                raw_response = result.get('response', '').strip()

                # Extract final title from DeepSeek-R1 response (skip thinking process)
                enhanced_title = self._extract_viral_title_from_response(raw_response)

                # Clean up the response
                enhanced_title = enhanced_title.replace('"', '').replace('\n', ' ')

                # Ensure proper viral format with underscores
                enhanced_title = enhanced_title.replace(' ', '_')

                # Truncate if too long (keep viral cliffhanger effect)
                if len(enhanced_title) > 50:
                    enhanced_title = enhanced_title[:47] + "..."

                return enhanced_title
            
        except Exception as e:
            self.logger.debug(f"AI title enhancement error: {e}")
        
        return highlight.title

    def _extract_viral_title_from_response(self, raw_response: str) -> str:
        """Extract the final viral title from DeepSeek-R1 response, skipping thinking process."""
        # DeepSeek-R1 includes reasoning - we need to extract just the final title

        # If response starts with <think>, it's likely all thinking - use fallback
        if raw_response.strip().startswith('<think>'):
            return "INSANE_AI_Generated_Viral_Content_Ready_For"

        import re

        # Remove any thinking blocks
        cleaned = re.sub(r'<think>.*?</think>', '', raw_response, flags=re.DOTALL)
        cleaned = cleaned.strip()

        # If nothing left after removing thinking, use fallback
        if not cleaned:
            return "VIRAL_Content_Generated_Ready_For_Upload"

        # Look for viral title patterns
        viral_patterns = [
            r'([A-Z][A-Z_]+[A-Za-z0-9_]{15,})',  # All caps with underscores
            r'([A-Z][A-Za-z_]+_[A-Z][A-Za-z_]+_[A-Za-z_]{8,})',  # Mixed case viral format
        ]

        for pattern in viral_patterns:
            matches = re.findall(pattern, cleaned)
            if matches:
                return max(matches, key=len)

        # Look for the last line that seems like a title
        lines = [line.strip() for line in cleaned.split('\n') if line.strip()]
        for line in reversed(lines):
            if any(skip in line.lower() for skip in ['here', 'title:', 'response:']):
                continue
            if len(line) > 10:
                # Convert to viral format
                viral_line = line.replace(' ', '_').upper()
                return viral_line[:47] if len(viral_line) > 47 else viral_line

        # Final fallback
        return "VIRAL_Content_Ready_For_Upload_Now"

    def _create_clip_metadata(
        self,
        highlight: Highlight,
        video_metadata: Dict[str, Any],
        enhanced_title: str,
        clip_path: Path
    ) -> Dict[str, Any]:
        """Create comprehensive metadata for a clip."""
        return {
            'clip_info': {
                'title': enhanced_title,
                'original_title': highlight.title,
                'description': highlight.description,
                'duration': highlight.duration,
                'confidence': highlight.confidence,
                'keywords': highlight.keywords,
                'file_path': str(clip_path),
                'file_size': clip_path.stat().st_size if clip_path.exists() else 0,
                'created_at': datetime.now().isoformat()
            },
            'timing': {
                'start_time': highlight.start_time,
                'end_time': highlight.end_time,
                'duration_seconds': highlight.duration
            },
            'source_video': {
                'title': video_metadata.get('title', ''),
                'url': video_metadata.get('webpage_url', ''),
                'uploader': video_metadata.get('uploader', ''),
                'duration': video_metadata.get('duration', 0),
                'view_count': video_metadata.get('view_count', 0)
            },
            'processing': {
                'extracted_at': datetime.now().isoformat(),
                'clipper_version': '1.0.0',
                'ai_model': self.config.highlight_detection_model
            }
        }
    
    def _generate_social_captions(
        self,
        clip_metadata: Dict[str, Any],
        output_dir: Path,
        clip_index: int
    ):
        """Generate social media captions for different platforms."""
        captions = {
            'twitter': self._generate_twitter_caption(clip_metadata),
            'instagram': self._generate_instagram_caption(clip_metadata),
            'tiktok': self._generate_tiktok_caption(clip_metadata),
            'youtube_shorts': self._generate_youtube_shorts_caption(clip_metadata)
        }
        
        # Save captions to file
        captions_file = output_dir / 'metadata' / f"clip_{clip_index + 1:02d}_captions.json"
        with open(captions_file, 'w', encoding='utf-8') as f:
            json.dump(captions, f, indent=2, ensure_ascii=False)
    
    def _generate_twitter_caption(self, metadata: Dict[str, Any]) -> str:
        """Generate Twitter-optimized caption."""
        title = metadata['clip_info']['title']
        keywords = metadata['clip_info']['keywords']
        
        # Twitter format: Title + hashtags (under 280 chars)
        hashtags = ' '.join([f"#{kw.replace(' ', '')}" for kw in keywords[:3]])
        caption = f"{title}\n\n{hashtags} #viral #trending"
        
        return caption[:280]  # Twitter character limit
    
    def _generate_instagram_caption(self, metadata: Dict[str, Any]) -> str:
        """Generate Instagram-optimized caption."""
        title = metadata['clip_info']['title']
        description = metadata['clip_info']['description']
        keywords = metadata['clip_info']['keywords']
        
        # Instagram format: Hook + description + hashtags
        hashtags = ' '.join([f"#{kw.replace(' ', '')}" for kw in keywords])
        caption = f"{title}\n\n{description}\n\n{hashtags} #reels #viral #trending"
        
        return caption
    
    def _generate_tiktok_caption(self, metadata: Dict[str, Any]) -> str:
        """Generate TikTok-optimized caption."""
        title = metadata['clip_info']['title']
        keywords = metadata['clip_info']['keywords']
        
        # TikTok format: Short hook + trending hashtags
        hashtags = ' '.join([f"#{kw.replace(' ', '')}" for kw in keywords[:5]])
        caption = f"{title}\n\n{hashtags} #fyp #viral #trending"
        
        return caption
    
    def _generate_youtube_shorts_caption(self, metadata: Dict[str, Any]) -> str:
        """Generate YouTube Shorts-optimized caption."""
        title = metadata['clip_info']['title']
        description = metadata['clip_info']['description']
        
        caption = f"{title}\n\n{description}\n\n#Shorts #viral #trending"
        
        return caption
    
    def _generate_summary_report(
        self,
        processed_clips: List[str],
        highlights: List[Highlight],
        metadata: Dict[str, Any],
        output_dir: Path
    ):
        """Generate summary report of the processing session."""
        report = {
            'session_info': {
                'processed_at': datetime.now().isoformat(),
                'total_clips': len(processed_clips),
                'source_video': metadata.get('title', ''),
                'source_url': metadata.get('webpage_url', ''),
                'output_directory': str(output_dir)
            },
            'clips_summary': [
                {
                    'index': i + 1,
                    'title': highlight.title,
                    'duration': highlight.duration,
                    'confidence': highlight.confidence,
                    'file_path': clip_path
                }
                for i, (clip_path, highlight) in enumerate(zip(processed_clips, highlights))
            ],
            'statistics': {
                'total_duration': sum(h.duration for h in highlights),
                'average_confidence': sum(h.confidence for h in highlights) / len(highlights) if highlights else 0,
                'highest_confidence': max((h.confidence for h in highlights), default=0),
                'total_file_size': sum(
                    Path(clip).stat().st_size for clip in processed_clips if Path(clip).exists()
                )
            }
        }
        
        # Save report
        report_file = output_dir / 'processing_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Summary report saved: {report_file}")
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility."""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        filename = filename.strip()[:50]
        filename = '_'.join(filename.split())
        
        return filename or "untitled"


if __name__ == "__main__":
    # Test the post-processor
    from config import ClipperConfig
    from highlight_detector import Highlight
    
    config = ClipperConfig()
    processor = PostProcessor(config)
    
    # Test with sample data
    sample_highlight = Highlight(
        start_time=30.0,
        end_time=60.0,
        title="Amazing Discovery",
        description="This moment shows an incredible breakthrough",
        confidence=0.95,
        keywords=["amazing", "discovery", "breakthrough"]
    )
    
    sample_metadata = {
        'title': 'Test Video',
        'webpage_url': 'https://youtube.com/watch?v=test',
        'uploader': 'Test Channel',
        'duration': 300
    }
    
    print(f"Post-processor initialized")
    print(f"Sample clip metadata structure created")
