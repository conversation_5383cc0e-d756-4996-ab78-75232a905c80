#!/usr/bin/env python3
"""
Daily Automation Script for Clipper_Neon
========================================

Complete automation workflow:
1. Process YouTube videos with perfect timestamps
2. Generate viral clips with AI
3. Auto-upload to social media platforms
4. Track performance and optimize

Run this daily for hands-off content creation.
"""

import sys
import json
import time
import os
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from config import ClipperConfig
from clipper_main import Clipper<PERSON>eon
from auto_uploader import AutoUploader, UploadJob
from auto_processor import AutoProcessor
from viral_organizer import ViralOrganizer


class DailyAutomation:
    """Handles complete daily automation workflow."""
    
    def __init__(self):
        """Initialize the automation system."""
        self.config = ClipperConfig()
        self.clipper = ClipperNeon()
        self.uploader = AutoUploader(self.config)
        self.processor = AutoProcessor(self.config)
        self.viral_organizer = ViralOrganizer(self.config)

        print("🚀 Clipper_Neon Daily Automation - VIRAL EDITION")
        print("=" * 55)
    
    def run_daily_workflow(self, urls: List[str], platforms: List[str] = None, skip_review: bool = False):
        """
        Run the complete daily automation workflow with optional review step.

        Args:
            urls: List of YouTube URLs to process
            platforms: List of platforms to upload to
            skip_review: Skip the manual review step for full automation
        """
        if platforms is None:
            platforms = ['youtube_shorts', 'tiktok']

        print(f"📅 Starting daily workflow: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📹 Processing {len(urls)} videos")
        print(f"📱 Target platforms: {', '.join(platforms)}")
        if skip_review:
            print("🤖 Review step: SKIPPED (full automation mode)")
        else:
            print("👁️  Review step: ENABLED (manual approval required)")
        print()

        all_sessions = []
        all_upload_jobs = []

        for i, url in enumerate(urls, 1):
            print(f"🎬 Processing video {i}/{len(urls)}: {url}")

            try:
                # Process video and get clips
                clips = self.clipper.process_video(url)

                if clips:
                    print(f"  ✅ Generated {len(clips)} clips")

                    # Get video metadata for proper organization
                    video_metadata = self.clipper.downloader.get_video_info(url)
                    video_title = video_metadata.get('title', f'Video {i}')

                    # Auto-organize clips into proper YT Cutter structure
                    session_data = self._auto_organize_clips(clips, video_title, url)
                    all_sessions.append(session_data)

                    print(f"  📁 Organized in: {session_data['video_folder']}")
                else:
                    print(f"  ⚠️  No clips generated")

            except Exception as e:
                print(f"  ❌ Processing failed: {e}")
                continue

            print()

        # Review and approval step (unless skipped)
        approved_clips = []
        if all_sessions and not skip_review:
            # Use the new viral review system
            approved_clips = self.review_viral_sessions(all_sessions)
        elif all_sessions:
            # Skip review - auto-approve all clips
            approved_clips = self.auto_approve_all_sessions(all_sessions)

        # Create upload jobs only for approved clips
        if approved_clips:
            for clip_path in approved_clips:
                for platform in platforms:
                    job = self.create_upload_job(clip_path, platform, "")
                    all_upload_jobs.append(job)

        # Upload all approved clips
        if all_upload_jobs:
            print(f"📤 Uploading {len(all_upload_jobs)} clips to platforms...")
            self.batch_upload(all_upload_jobs)

        # Generate daily report
        self.generate_daily_report(approved_clips, all_upload_jobs)

        print("🎉 Daily automation workflow completed!")

    def generate_clips_summary(self, clips: List[str], output_dir: str) -> str:
        """Generate a comprehensive summary of created clips for review."""
        summary_data = {
            "review_session": {
                "created_at": datetime.now().isoformat(),
                "total_clips": len(clips),
                "output_directory": output_dir
            },
            "clips": []
        }

        for i, clip_path in enumerate(clips, 1):
            clip_file = Path(clip_path)
            if not clip_file.exists():
                continue

            # Extract metadata from filename and file stats
            file_size_mb = round(clip_file.stat().st_size / 1024 / 1024, 2)

            # Try to extract timing info from filename (format: clip_XX_title.mp4)
            filename_parts = clip_file.stem.split('_')
            clip_info = {
                "clip_number": i,
                "filename": clip_file.name,
                "file_path": str(clip_path),
                "file_size_mb": file_size_mb,
                "title": "Unknown Title",
                "estimated_duration": "Unknown"
            }

            # Extract title from filename if possible
            if len(filename_parts) >= 3:
                title_part = '_'.join(filename_parts[2:]).replace('_', ' ')
                clip_info["title"] = title_part

            summary_data["clips"].append(clip_info)

        # Save summary to file
        summary_file = Path(output_dir) / "clips_summary.json"
        with open(summary_file, 'w') as f:
            json.dump(summary_data, f, indent=2)

        return str(summary_file)

    def review_and_approve_clips(self, clips: List[str], output_dir: str) -> List[str]:
        """
        Present clips for review and get user approval before upload.
        Returns list of approved clips (user can remove unwanted ones).
        """
        if not clips:
            print("⚠️  No clips to review.")
            return clips

        print("\n" + "="*60)
        print("🔍 CLIP REVIEW & APPROVAL")
        print("="*60)

        # Generate summary
        summary_file = self.generate_clips_summary(clips, output_dir)
        print(f"📋 Clips summary saved: {summary_file}")
        print()

        # Display clip information for review
        print(f"📁 Review clips in: {output_dir}")
        print(f"🎬 Total clips generated: {len(clips)}")
        print()

        for i, clip_path in enumerate(clips, 1):
            clip_file = Path(clip_path)
            if clip_file.exists():
                file_size_mb = round(clip_file.stat().st_size / 1024 / 1024, 2)
                filename_parts = clip_file.stem.split('_')
                title = '_'.join(filename_parts[2:]).replace('_', ' ') if len(filename_parts) >= 3 else "Unknown"

                print(f"  {i}. {clip_file.name}")
                print(f"     Title: {title}")
                print(f"     Size: {file_size_mb} MB")
                print(f"     Path: {clip_path}")
                print()

        print("📝 REVIEW INSTRUCTIONS:")
        print("  1. Check the clips in the output folder above")
        print("  2. Delete any clips you don't want to upload")
        print("  3. Press ENTER to continue with upload")
        print("  4. Press CTRL+C to abort the upload process")
        print()

        try:
            input("✋ Press ENTER when ready to continue with upload (or CTRL+C to abort): ")
        except KeyboardInterrupt:
            print("\n❌ Upload process aborted by user.")
            return []

        # Check which clips still exist after user review
        approved_clips = []
        for clip_path in clips:
            if Path(clip_path).exists():
                approved_clips.append(clip_path)
            else:
                print(f"🗑️  Removed from upload: {Path(clip_path).name}")

        print(f"\n✅ Proceeding with {len(approved_clips)} approved clips")
        return approved_clips

    def review_viral_sessions(self, sessions: List[Dict[str, Any]]) -> List[str]:
        """Review viral sessions with enhanced analytics."""
        print("\n" + "="*70)
        print("🎬 ELITE VIRAL CLIP REVIEW - V.I.R.A.L. FRAMEWORK")
        print("="*70)

        total_clips = sum(len(session['clips']) for session in sessions)
        print(f"📊 Sessions to review: {len(sessions)}")
        print(f"🎯 Total clips generated: {total_clips}")

        # Display session analytics
        for i, session in enumerate(sessions, 1):
            session_info = session['session_info']
            analytics = session.get('viral_analytics', {})

            print(f"\n🎬 SESSION {i}: {session_info['source_video']}")
            print(f"   📁 Folder: {session_info['session_folder']}")
            print(f"   🎯 Clips: {session_info['total_clips']}")
            print(f"   📊 Avg Viral Score: {analytics.get('average_viral_score', 0):.1f}")
            print(f"   🏆 Max Viral Score: {analytics.get('highest_viral_score', 0):.1f}")

            # Show top viral components
            top_components = analytics.get('top_viral_components', [])
            if top_components:
                print(f"   🎯 Top Components: {', '.join(top_components[:3])}")

        print("\n🔍 VIRAL REVIEW INSTRUCTIONS:")
        print("=" * 40)
        print("1. Open session folders above to preview clips")
        print("2. Each clip shows VIRAL score and reasoning")
        print("3. Delete any clips you don't want to upload")
        print("4. High viral score clips (>150) are prioritized")
        print("5. Press ENTER to continue with upload")
        print()

        try:
            input("✋ Press ENTER when ready to continue with upload (or CTRL+C to abort): ")
        except KeyboardInterrupt:
            print("\n❌ Upload process aborted by user.")
            return []

        # Collect approved clips from all sessions
        approved_clips = []
        for session in sessions:
            session_folder = Path(session['session_info']['session_folder'])
            clips_folder = session_folder / "clips"

            for clip_data in session['clips']:
                clip_path = Path(clip_data['file_path'])
                if clip_path.exists():
                    approved_clips.append(str(clip_path))
                else:
                    print(f"🗑️  Removed from upload: {clip_path.name}")

        print(f"\n✅ Proceeding with {len(approved_clips)} approved VIRAL clips")
        return approved_clips

    def auto_approve_all_sessions(self, sessions: List[Dict[str, Any]]) -> List[str]:
        """Auto-approve all clips when review is skipped."""
        print("🤖 Auto-approving all clips (review skipped)")

        approved_clips = []
        for session in sessions:
            for clip_data in session['clips']:
                clip_path = Path(clip_data['file_path'])
                if clip_path.exists():
                    approved_clips.append(str(clip_path))

        print(f"✅ Auto-approved {len(approved_clips)} clips")
        return approved_clips

    def _auto_organize_clips(self, clips: List[str], video_title: str, source_url: str) -> Dict[str, Any]:
        """Auto-organize clips into proper YT Cutter structure."""
        import shutil

        # Find next available video folder number
        video_folder_number = self._get_next_video_folder_number()

        # Create video-specific folder: "1. Video Title/"
        clean_title = self._sanitize_filename(video_title)
        video_folder_name = f"{video_folder_number}. {clean_title}"
        video_folder = Path(self.config.output_dir) / video_folder_name
        video_folder.mkdir(exist_ok=True)

        organized_clips = []

        for i, clip_path in enumerate(clips, 1):
            if not Path(clip_path).exists():
                continue

            # Extract title from clip filename
            clip_filename = Path(clip_path).stem
            if "Unexpected_Breakup_Announcement" in clip_filename:
                clip_title = "Unexpected Breakup Announcement"
            elif "Breakup_Announcement" in clip_filename:
                clip_title = "Breakup Announcement"
            elif "Highlight" in clip_filename:
                clip_title = f"Highlight {i}"
            else:
                clip_title = f"Moment {i}"

            # Proper YT Cutter naming: "Clip 1 - Descriptive Name.mp4"
            new_filename = f"Clip {i} - {clip_title}.mp4"
            new_clip_path = video_folder / new_filename

            # Move clip to video folder
            shutil.move(clip_path, new_clip_path)

            organized_clips.append({
                "clip_number": i,
                "filename": new_filename,
                "file_path": str(new_clip_path),
                "file_size_mb": round(new_clip_path.stat().st_size / 1024 / 1024, 2),
                "title": clip_title
            })

        # Create clips_info.json
        clips_info = {
            "video_folder": video_folder_name,
            "source_video": video_title,
            "source_url": source_url,
            "created_at": datetime.now().isoformat(),
            "total_clips": len(organized_clips),
            "clips": organized_clips
        }

        clips_info_file = video_folder / "clips_info.json"
        with open(clips_info_file, 'w', encoding='utf-8') as f:
            json.dump(clips_info, f, indent=2)

        return clips_info

    def _get_next_video_folder_number(self) -> int:
        """Find the next available video folder number."""
        output_dir = Path(self.config.output_dir)
        existing_folders = []

        for folder in output_dir.iterdir():
            if folder.is_dir() and folder.name[0].isdigit():
                try:
                    number = int(folder.name.split('.')[0])
                    existing_folders.append(number)
                except (ValueError, IndexError):
                    continue

        return max(existing_folders, default=0) + 1

    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility."""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename[:50].strip()

    def create_upload_job(self, clip_path: str, platform: str, source_url: str) -> UploadJob:
        """Create an upload job for a clip."""
        clip_name = Path(clip_path).stem
        
        # Extract title from filename or use default
        if "_" in clip_name:
            title_part = clip_name.split("_", 2)[-1].replace("_", " ")
            title = title_part[:50]  # Platform title limits
        else:
            title = "Viral Moment"
        
        # Platform-specific descriptions
        descriptions = {
            'youtube_shorts': f"{title} #Shorts #Viral #Trending",
            'tiktok': f"{title} #fyp #viral #trending #shorts",
            'instagram': f"{title} #reels #viral #trending #explore"
        }
        
        return UploadJob(
            platform=platform,
            video_path=clip_path,
            title=title,
            description=descriptions.get(platform, title),
            tags=['viral', 'trending', 'shorts']
        )
    
    def batch_upload(self, upload_jobs: List[UploadJob]):
        """Upload clips in batches to avoid rate limiting."""
        # Group by platform
        platform_jobs = {}
        for job in upload_jobs:
            if job.platform not in platform_jobs:
                platform_jobs[job.platform] = []
            platform_jobs[job.platform].append(job)
        
        # Upload to each platform
        for platform, jobs in platform_jobs.items():
            print(f"  📱 Uploading {len(jobs)} clips to {platform}")
            
            try:
                results = self.uploader.upload_batch(jobs)
                successful = sum(1 for success in results.values() if success)
                print(f"    ✅ {successful}/{len(jobs)} uploads successful")
                
                # Wait between platforms to avoid rate limiting
                if len(platform_jobs) > 1:
                    print("    ⏳ Waiting 30s before next platform...")
                    time.sleep(30)
                
            except Exception as e:
                print(f"    ❌ Upload failed for {platform}: {e}")
    
    def generate_daily_report(self, clips: List[str], upload_jobs: List[UploadJob]):
        """Generate a daily automation report."""
        report = {
            'date': datetime.now().isoformat(),
            'summary': {
                'total_clips_generated': len(clips),
                'total_upload_jobs': len(upload_jobs),
                'platforms': list(set(job.platform for job in upload_jobs)),
                'clips_per_platform': {}
            },
            'clips': [
                {
                    'path': clip,
                    'filename': Path(clip).name,
                    'size_mb': round(Path(clip).stat().st_size / 1024 / 1024, 2) if Path(clip).exists() else 0
                }
                for clip in clips
            ],
            'upload_jobs': [
                {
                    'platform': job.platform,
                    'title': job.title,
                    'video_file': Path(job.video_path).name
                }
                for job in upload_jobs
            ]
        }
        
        # Count clips per platform
        for job in upload_jobs:
            platform = job.platform
            if platform not in report['summary']['clips_per_platform']:
                report['summary']['clips_per_platform'][platform] = 0
            report['summary']['clips_per_platform'][platform] += 1
        
        # Save report
        reports_dir = Path(self.config.output_dir) / "daily_reports"
        reports_dir.mkdir(exist_ok=True)
        
        report_file = reports_dir / f"daily_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"📊 Daily report saved: {report_file}")
        
        # Print summary
        print("\n📈 Daily Summary:")
        print(f"  🎬 Clips generated: {len(clips)}")
        print(f"  📤 Upload jobs created: {len(upload_jobs)}")
        for platform, count in report['summary']['clips_per_platform'].items():
            print(f"    {platform}: {count} clips")


def main():
    """Main entry point for daily automation."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Clipper_Neon Daily Automation")
    
    parser.add_argument(
        "urls",
        nargs="+",
        help="YouTube URLs to process (or path to file containing URLs)"
    )
    
    parser.add_argument(
        "--platforms",
        nargs="+",
        default=['youtube_shorts', 'tiktok'],
        help="Platforms to upload to"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Process videos but don't upload"
    )

    parser.add_argument(
        "--skip-review",
        action="store_true",
        help="Skip manual review step for full automation"
    )
    
    args = parser.parse_args()
    
    # Handle URL file input
    urls = []
    for url_input in args.urls:
        if Path(url_input).exists():
            # Read URLs from file
            with open(url_input, 'r') as f:
                file_urls = [line.strip() for line in f if line.strip()]
            urls.extend(file_urls)
        else:
            # Direct URL
            urls.append(url_input)
    
    if not urls:
        print("❌ No URLs provided")
        return 1
    
    # Initialize automation
    automation = DailyAutomation()
    
    if args.dry_run:
        print("🧪 DRY RUN MODE - No uploads will be performed")
        # Process videos only
        for url in urls:
            try:
                clips = automation.clipper.process_video(url)
                print(f"✅ {url}: {len(clips)} clips generated")
            except Exception as e:
                print(f"❌ {url}: {e}")
    else:
        # Run full automation with optional review step
        automation.run_daily_workflow(urls, args.platforms, args.skip_review)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())


# Example usage:
"""
# Process single video with review step (DEFAULT - RECOMMENDED)
python daily_automation.py "https://youtube.com/watch?v=VIDEO_ID"

# Process multiple videos with review
python daily_automation.py "https://youtube.com/watch?v=ID1" "https://youtube.com/watch?v=ID2"

# Process from file with review
python daily_automation.py urls.txt

# Custom platforms with review
python daily_automation.py "URL" --platforms youtube_shorts tiktok instagram

# Skip review for full automation (99% hands-off)
python daily_automation.py "URL" --skip-review

# Dry run (no uploads, no review needed)
python daily_automation.py "URL" --dry-run

# Full automation without any manual intervention
python daily_automation.py "URL" --skip-review --platforms youtube_shorts tiktok
"""
