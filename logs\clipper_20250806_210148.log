2025-08-06 21:01:48,739 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 21:01:50,800 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 21:01:50,803 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 21:01:52,847 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 21:01:52,848 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 21:01:52,848 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:01:52,848 - clipper_main - INFO - Step 1: Extracting transcript...
2025-08-06 21:01:53,261 - transcription_engine - INFO - Opening YouTube video: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:02:04,202 - transcription_engine - INFO - Expanding description with: tp-yt-paper-button#expand
2025-08-06 21:02:05,265 - transcription_engine - INFO - Found transcript button after expansion: text=Show transcript
2025-08-06 21:02:05,300 - transcription_engine - INFO - Waiting for transcript segments to load...
2025-08-06 21:02:06,350 - transcription_engine - INFO - Extracting transcript segments...
2025-08-06 21:02:09,347 - transcription_engine - INFO - [SUCCESS] Extracted 340 transcript segments via UI!
2025-08-06 21:02:09,359 - clipper_main - INFO - [SUCCESS] Transcript extracted: 340 segments
2025-08-06 21:02:09,359 - clipper_main - INFO - Step 2: Downloading video...
2025-08-06 21:02:09,359 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:02:09,359 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:02:13,592 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 21:02:13,593 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 21:02:24,412 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 21:02:24,412 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 21:02:24,412 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 21:02:53,019 - highlight_detector - INFO - Parsed 1 highlights from AI response
2025-08-06 21:02:53,019 - highlight_detector - INFO - Raw highlights before filtering: 1
2025-08-06 21:02:53,019 - highlight_detector - INFO - [OK] Accepted highlight: 'INSANE_Breakup_LIVE_She_EXPOSED_Everything_On' (45.2s-78.5s) conf=0.50
2025-08-06 21:02:53,020 - highlight_detector - INFO - Detected 1 valid highlights
2025-08-06 21:02:53,020 - clipper_main - INFO - Step 4: Creating target folder and extracting clips...
2025-08-06 21:02:53,020 - clipper_main - INFO - Created target folder: 2. we broke up..
2025-08-06 21:02:53,020 - video_clipper - INFO - Extracting clip 1: INSANE_Breakup_LIVE_She_EXPOSED_Everything_On (45.2s-78.5s)
2025-08-06 21:02:53,050 - video_clipper - ERROR - ffmpeg failed: ffmpeg version 7.1.1-essentials_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
  built with gcc 14.2.0 (Rev1, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.101 / 61. 19.101
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
Trailing option(s) found in the command: may be ignored.
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2mp41
    encoder         : Lavf61.7.100
  Duration: 00:11:52.53, start: 0.000000, bitrate: 1977 kb/s
  Stream #0:0[0x1](und): Video: vp9 (Profile 0) (vp09 / 0x39307076), yuv420p(tv, bt709), 1920x1080, 1837 kb/s, 60 fps, 60 tbr, 16k tbn (default)
      Metadata:
        handler_name    : ISO Media file produced by Google Inc. Created on: 08/06/2025.
        vendor_id       : [0][0][0][0]
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 127 kb/s (default)
      Metadata:
        handler_name    : ISO Media file produced by Google Inc.
        vendor_id       : [0][0][0][0]
[out#0/mp4 @ 00000196d18d9e80] Error opening output C:\AI-Hub\Applications\Clipper_Neon\data_out\2. we broke up..\clip_01_INSANE_Breakup_LIVE_She_EXPOSED_Everything_On.mp4: No such file or directory
Error opening output file C:\AI-Hub\Applications\Clipper_Neon\data_out\2. we broke up..\clip_01_INSANE_Breakup_LIVE_She_EXPOSED_Everything_On.mp4.
Error opening output files: No such file or directory

2025-08-06 21:02:53,050 - clipper_main - ERROR - Error processing video https://www.youtube.com/watch?v=xKa_6kzOl0M: Command '['C:\\AI-Hub\\Core\\Utilities\\ffmpeg.exe', '-i', 'C:\\AI-Hub\\Applications\\Clipper_Neon\\temp\\xKa_6kzOl0M.mp4', '-ss', '45.2', '-t', '33.3', '-c:v', 'libx264', '-c:a', 'aac', '-preset', 'fast', '-crf', '23', '-movflags', '+faststart', '-y', 'C:\\AI-Hub\\Applications\\Clipper_Neon\\data_out\\2. we broke up..\\clip_01_INSANE_Breakup_LIVE_She_EXPOSED_Everything_On.mp4', '-vf', 'scale=-2:720']' returned non-zero exit status 4294967294.
