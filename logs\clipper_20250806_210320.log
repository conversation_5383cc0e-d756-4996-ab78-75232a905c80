2025-08-06 21:03:20,958 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 21:03:22,999 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 21:03:23,000 - transcription_engine - INFO - Direct YouTube transcript extraction ready
2025-08-06 21:03:25,051 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 21:03:25,052 - auto_processor - INFO - File watcher setup for: C:\AI-Hub\Applications\Clipper_Neon\data_in
2025-08-06 21:03:25,052 - clipper_main - INFO - Starting processing for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:03:25,052 - clipper_main - INFO - Step 1: Extracting transcript...
2025-08-06 21:03:25,431 - transcription_engine - INFO - Opening YouTube video: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:03:32,967 - transcription_engine - INFO - Expanding description with: tp-yt-paper-button#expand
2025-08-06 21:03:34,037 - transcription_engine - INFO - Found transcript button after expansion: text=Show transcript
2025-08-06 21:03:34,079 - transcription_engine - INFO - Waiting for transcript segments to load...
2025-08-06 21:03:35,129 - transcription_engine - INFO - Extracting transcript segments...
2025-08-06 21:03:38,056 - transcription_engine - INFO - [SUCCESS] Extracted 340 transcript segments via UI!
2025-08-06 21:03:38,066 - clipper_main - INFO - [SUCCESS] Transcript extracted: 340 segments
2025-08-06 21:03:38,067 - clipper_main - INFO - Step 2: Downloading video...
2025-08-06 21:03:38,067 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:03:38,067 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=xKa_6kzOl0M
2025-08-06 21:03:42,213 - video_downloader - INFO - Video info retrieved: we broke up.. (713s)
2025-08-06 21:03:42,214 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 21:03:46,159 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\xKa_6kzOl0M.mp4
2025-08-06 21:03:46,159 - clipper_main - INFO - Step 3: Detecting highlights with AI...
2025-08-06 21:03:46,159 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 21:04:44,735 - highlight_detector - ERROR - Failed to parse JSON response: Expecting property name enclosed in double quotes: line 8 column 29 (char 225)
2025-08-06 21:04:44,735 - highlight_detector - WARNING - Using fallback highlight detection
2025-08-06 21:04:44,736 - highlight_detector - INFO - Raw highlights before filtering: 3
2025-08-06 21:04:44,736 - highlight_detector - INFO - [OK] Accepted highlight: 'Highlight 1' (10.0s-55.0s) conf=0.60
2025-08-06 21:04:44,736 - highlight_detector - INFO - [OK] Accepted highlight: 'Highlight 2' (188.2s-233.2s) conf=0.60
2025-08-06 21:04:44,736 - highlight_detector - INFO - [OK] Accepted highlight: 'Highlight 3' (366.5s-411.5s) conf=0.60
2025-08-06 21:04:44,736 - highlight_detector - INFO - Detected 3 valid highlights
2025-08-06 21:04:44,736 - clipper_main - INFO - Step 4: Creating target folder and extracting clips...
2025-08-06 21:04:44,737 - clipper_main - INFO - Created target folder: 3. we broke up
2025-08-06 21:04:44,737 - video_clipper - INFO - Extracting clip 1: Highlight 1 (10.0s-55.0s)
2025-08-06 21:04:52,511 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\3. we broke up\clip_01_Highlight_1.mp4 (9.9 MB)
2025-08-06 21:04:52,511 - video_clipper - INFO - Extracting clip 2: Highlight 2 (188.2s-233.2s)
2025-08-06 21:05:06,718 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\3. we broke up\clip_02_Highlight_2.mp4 (12.6 MB)
2025-08-06 21:05:06,718 - video_clipper - INFO - Extracting clip 3: Highlight 3 (366.5s-411.5s)
2025-08-06 21:05:27,372 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\3. we broke up\clip_03_Highlight_3.mp4 (12.7 MB)
2025-08-06 21:05:27,372 - clipper_main - INFO - Step 5: Post-processing clips with viral enhancement...
2025-08-06 21:05:32,213 - clipper_main - WARNING - Failed to enhance title for clip 1: Highlight.__init__() got an unexpected keyword argument 'viral_score'
2025-08-06 21:05:36,876 - clipper_main - WARNING - Failed to enhance title for clip 2: Highlight.__init__() got an unexpected keyword argument 'viral_score'
2025-08-06 21:05:41,591 - clipper_main - WARNING - Failed to enhance title for clip 3: Highlight.__init__() got an unexpected keyword argument 'viral_score'
2025-08-06 21:05:41,591 - clipper_main - INFO - Step 6: Generating clips metadata...
2025-08-06 21:05:41,592 - clipper_main - INFO - Generated clips metadata: C:\AI-Hub\Applications\Clipper_Neon\data_out\3. we broke up\clips_info.json
2025-08-06 21:05:41,592 - clipper_main - INFO - Successfully generated 3 clips
