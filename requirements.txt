# Clipper_Neon Requirements
# Core dependencies for YouTube clipping automation

# AI/ML Libraries
openai-whisper>=20231117
torch>=2.0.0
transformers>=4.30.0

# Audio/Video Processing
ffmpeg-python>=0.2.0

# Web and API
requests>=2.31.0
urllib3>=2.0.0

# Data Processing
numpy>=1.24.0
pandas>=2.0.0

# Optional: Enhanced Speech Recognition
SpeechRecognition>=3.10.0
pydub>=0.25.1

# Optional: Advanced Audio Processing
librosa>=0.10.0
soundfile>=0.12.0

# Development and Testing
pytest>=7.4.0
pytest-cov>=4.1.0

# Utilities
python-dotenv>=1.0.0
tqdm>=4.65.0
pathlib2>=2.3.7

# Note: The following are already available in the base environment:
# - ollama (for DeepSeek-R1 integration)
# - crewai
# - langchain
# - litellm
